package com.jubiq.loreal.notifications.services;

import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.FileConfiguration;
import com.jubiq.loreal.LorealConfiguration;
import com.jubiq.loreal.advertisings.models.Advertising;
import com.jubiq.loreal.advertisings.models.AdvertisingExportedField;
import com.jubiq.loreal.cra.models.CraRequest;
import com.jubiq.loreal.cra.models.CraRequestExportedField;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.labels.models.Label;
import com.jubiq.loreal.labels.models.LabelExportedField;
import com.jubiq.loreal.notifications.dao.NotificationDao;
import com.jubiq.loreal.notifications.models.*;
import com.jubiq.loreal.notifications.models.enumerations.ExportedField;
import com.jubiq.loreal.notifications.models.enumerations.Presentation;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by vietnq2 on 1/19/16.
 */
@Singleton
public class ExportService {
    private static final Logger logger = LoggerFactory.getLogger(ExportService.class);

    private String fileDir;
    private FactoryService factoryService;
    private ExporterFactoryService exporterFactoryService;
    private IngredientService ingredientService;
    private ProductTypeService productTypeService;
    private CountryService countryService;
    private String downloadUrl;

    @Inject
    public ExportService(LorealConfiguration configuration,
                         FileConfiguration fileConfiguration,
                         FactoryService factoryService,
                         ExporterFactoryService exporterFactoryService,
                         IngredientService ingredientService,
                         ProductTypeService productTypeService,
                         CountryService countryService) {
        this.fileDir = fileConfiguration.fileDir;
        this.factoryService = factoryService;
        this.exporterFactoryService = exporterFactoryService;
        this.ingredientService = ingredientService;
        this.productTypeService = productTypeService;
        this.countryService = countryService;
        this.downloadUrl = configuration.serverUrl + "files/download/";
    }

    public File exportToDavTemplate(Notification notification) throws IOException {
        File file = new File(fileDir + "/dav_form_" + notification.id + System.currentTimeMillis() + ".xlsx");
        FileOutputStream output = new FileOutputStream(file);

//        InputStream is = getClass().getClassLoader().getResourceAsStream("templates/dav_form_3.xlsx");
        InputStream is = getClass().getClassLoader().getResourceAsStream("templates/dav_form_4.xlsx");

        Workbook wb = new XSSFWorkbook(is);

        fillInfoSheet(wb.getSheetAt(0), notification);
        fillManufacturer(wb.getSheetAt(1), notification);
        fillAssembler(wb.getSheetAt(2), notification);
        fillIngredients(wb.getSheetAt(3), notification);
        fillExporter(wb.getSheetAt(4), notification);

        wb.setForceFormulaRecalculation(true);
        wb.write(output);
        output.close();
        return file;
    }

    private void fillInfoSheet(Sheet sheet, Notification notification) {
        String yes = sheet.getWorkbook().getSheetAt(5).getRow(0).getCell(0).getStringCellValue();

        Cell brandNameCell = sheet.getRow(1).getCell(1) == null ? sheet.getRow(1).createCell(1) : sheet.getRow(1).getCell(1);
        brandNameCell.setCellValue(notification.brandName);
        brandNameCell.setCellStyle(cellStyle(sheet.getWorkbook()));

        Cell productNameCell = sheet.getRow(1).getCell(8) == null ? sheet.getRow(1).createCell(8) : sheet.getRow(1).getCell(8);
        productNameCell.setCellValue(StringEscapeUtils.unescapeHtml4(notification.productName));
        productNameCell.setCellStyle(cellStyle(sheet.getWorkbook()));

        Cell danh_sach_dang_hoac_mau = sheet.getRow(2).getCell(1);
        List<String> listShades = new ArrayList<String>();
        String commonShadeName = "";
        for (PresentationDetail detail : notification.presentationDetails) {
            if (detail.commonShadeName != null && detail.commonShadeName != "") {
                commonShadeName = StringEscapeUtils.unescapeHtml4(detail.commonShadeName);
            }
            if (detail.shadeName != null && !listShades.contains(detail.shadeName)) {
                listShades.add(StringEscapeUtils.unescapeHtml4(detail.shadeName));
            }
        }
        Collections.sort(listShades);
        String dang_sach_dang_hoac_mau = String.join("; ", listShades);
        if (commonShadeName != "") {
            dang_sach_dang_hoac_mau = commonShadeName + "(" + String.join("; ", listShades) + ")";
        }
        danh_sach_dang_hoac_mau.setCellValue(dang_sach_dang_hoac_mau);
        danh_sach_dang_hoac_mau.setCellStyle(cellStyle(sheet.getWorkbook()));

        for (long productTypeId : notification.productTypeIds) {
            ProductType productType = productTypeService.get(productTypeId);
            if (productType.davCode >= 1 && productType.davCode <= 27) {
                Cell dang_san_pham = sheet.getRow((2 + productType.davCode)).getCell(8);
                dang_san_pham.setCellValue(yes);
            }
        }

//        sheet.getRow(25).getCell(1).setCellValue(getIntendedUse(notification));
        // dav_form_4
        sheet.getRow(31).getCell(1).setCellValue(getIntendedUse(notification));

        if (notification.presentationType == Presentation.SINGLE) {
//            Cell dang_trinh_bay = sheet.getRow(26).getCell(8) == null ? sheet.getRow(26).createCell(8) : sheet.getRow(26).getCell(8);
            // dav_form_4
            Cell dang_trinh_bay = sheet.getRow(32).getCell(8) == null ? sheet.getRow(32).createCell(8) : sheet.getRow(32).getCell(8);
            dang_trinh_bay.setCellValue(yes);
        }

        if (notification.presentationType == Presentation.RANGE_OF_COLORS) {
//            Cell dang_trinh_bay = sheet.getRow(27).getCell(8) == null ? sheet.getRow(27).createCell(8) : sheet.getRow(27).getCell(8);
            // dav_form_4
            Cell dang_trinh_bay = sheet.getRow(33).getCell(8) == null ? sheet.getRow(33).createCell(8) : sheet.getRow(33).getCell(8);
            dang_trinh_bay.setCellValue(yes);
        }

        if (notification.presentationType == Presentation.PALETTE) {
//            Cell dang_trinh_bay = sheet.getRow(28).getCell(8) == null ? sheet.getRow(28).createCell(8) : sheet.getRow(28).getCell(8);
            // dav_form_4
            Cell dang_trinh_bay = sheet.getRow(34).getCell(8) == null ? sheet.getRow(34).createCell(8) : sheet.getRow(34).getCell(8);
            dang_trinh_bay.setCellValue(yes);
        }

        if (notification.presentationType == Presentation.COMBINATION) {
//            Cell dang_trinh_bay = sheet.getRow(29).getCell(8) == null ? sheet.getRow(29).createCell(8) : sheet.getRow(29).getCell(8);
            // dav_form_4
            Cell dang_trinh_bay = sheet.getRow(35).getCell(8) == null ? sheet.getRow(35).createCell(8) : sheet.getRow(35).getCell(8);
            dang_trinh_bay.setCellValue(yes);
        }

        if (notification.presentationType == Presentation.OTHER) {
//            Cell dang_trinh_bay = sheet.getRow(30).getCell(8) == null ? sheet.getRow(30).createCell(8) : sheet.getRow(30).getCell(8);
            // dav_form_4
            Cell dang_trinh_bay = sheet.getRow(36).getCell(8) == null ? sheet.getRow(36).createCell(8) : sheet.getRow(36).getCell(8);
            dang_trinh_bay.setCellValue(yes);

//            Cell dang_trinh_bay_khac = sheet.getRow(31).getCell(8) == null ? sheet.getRow(31).createCell(8) : sheet.getRow(31).getCell(8);
            // dav_form_4
            Cell dang_trinh_bay_khac = sheet.getRow(37).getCell(1) == null ? sheet.getRow(37).createCell(1) : sheet.getRow(37).getCell(1);
            dang_trinh_bay_khac.setCellValue(notification.otherPresentationType);
        }

        // fill ngay ky ho so
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        Cell signDate = sheet.getRow(52).getCell(8) == null ? sheet.getRow(52).createCell(8) : sheet.getRow(52).getCell(8);
        signDate.setCellValue(dateFormat.format(new Date()));
    }

    private String getIntendedUse(Notification notification) {
        String intendedUse;
        if (notification.presentationType == Presentation.COMBINATION || notification.presentationType == Presentation.OTHER) {
            StringBuilder sb = new StringBuilder(StringEscapeUtils.unescapeHtml4(notification.intendedUse)).append("\n");
            for (PresentationDetail detail : notification.presentationDetails) {
                sb.append("- ").append(StringEscapeUtils.unescapeHtml4(detail.shadeName)).append(": ").append(StringEscapeUtils.unescapeHtml4(detail.individualIntendedUse)).append("\n");
            }
            intendedUse = sb.toString();
        } else {
            if (notification.presentationDetails.size() > 0) {
                intendedUse = StringEscapeUtils.unescapeHtml4(notification.presentationDetails.get(0).individualIntendedUse);
            } else {
                intendedUse = StringEscapeUtils.unescapeHtml4(notification.intendedUse);
            }
        }
        return intendedUse;
    }

    private void fillManufacturer(Sheet sheet, Notification notification) {
        Map<Long, Factory> factoryMap = new HashMap<Long, Factory>();
        int order = 1;
        int rowIndex = 2;
        for (PresentationDetail detail : notification.presentationDetails) {
            if (!factoryMap.containsKey(detail.manufacturerId)) {
                try {
                    Factory manufacturer = factoryService.get(detail.manufacturerId);
                    factoryMap.put(detail.manufacturerId, manufacturer);

                } catch (EntityNotFoundException e) {
                    Factory manufacturer = new Factory();
                    manufacturer.name = detail.manufacturerName;
                    manufacturer.address = detail.manufacturerAddress;
                    manufacturer.phoneNumber = detail.manufacturerPhoneNumber;
                    manufacturer.faxNumber = detail.manufacturerFaxNumber;
                    manufacturer.countryId = detail.manufacturerCountryId;

                    factoryMap.put(detail.manufacturerId, manufacturer);
                }
            }
        }
        for (Long id : factoryMap.keySet()) {
            Row row = sheet.getRow(rowIndex);
            for (int i = 0; i < 7; i++) {
                if (row.getCell(i) == null) {
                    row.createCell(i);
                }
            }
            Factory manufacturer = factoryMap.get(id);
            row.getCell(0).setCellValue(order);
            row.getCell(1).setCellValue(manufacturer.name);
            row.getCell(2).setCellValue(manufacturer.address);
            row.getCell(3).setCellValue(manufacturer.phoneNumber);
            row.getCell(4).setCellValue(manufacturer.faxNumber);

            Country country = countryService.get(manufacturer.countryId);
            if (country.davCode != null) {
//                row.getCell(6).setCellValue(country.davCode);
                //dav_form_4
                row.getCell(5).setCellValue(country.davCode);
            }

            order++;
            rowIndex++;
        }
        sheet.autoSizeColumn((short) 1);
        sheet.autoSizeColumn((short) 2);
    }

    private void fillAssembler(Sheet sheet, Notification notification) {
        String yes = sheet.getWorkbook().getSheetAt(5).getRow(0).getCell(0).getStringCellValue();
        Map<Long, Factory> factoryMap = new HashMap<Long, Factory>();
        int order = 1;
        int rowIndex = 2;
        for (PresentationDetail detail : notification.presentationDetails) {
            if (!factoryMap.containsKey(detail.manufacturerId)) {
                try {
                    Factory assembler = factoryService.get(detail.assemblerId);
                    factoryMap.put(detail.assemblerId, assembler);
                } catch (EntityNotFoundException e) {
                    Factory assembler = new Factory();
                    assembler.name = detail.assemblerName;
                    assembler.address = detail.assemblerAddress;
                    assembler.phoneNumber = detail.assemblerPhoneNumber;
                    assembler.faxNumber = detail.assemblerFaxNumber;
                    assembler.countryId = detail.assemblerCountryId;
                    factoryMap.put(detail.assemblerId, assembler);
                }

            }
        }
        for (Long id : factoryMap.keySet()) {
            Row row = sheet.getRow(rowIndex);
            Factory assembler = factoryMap.get(id);
            for (int i = 0; i < 10; i++) {
                if (row.getCell(i) == null) {
                    row.createCell(i);
                }
            }

            row.getCell(0).setCellValue(order);
            row.getCell(1).setCellValue(assembler.name);
            row.getCell(2).setCellValue(assembler.address);
            row.getCell(3).setCellValue(assembler.phoneNumber);
            row.getCell(4).setCellValue(assembler.faxNumber);
            row.getCell(5).setCellValue(yes);

            Country country = countryService.get(assembler.countryId);
            if (country.davCode != null) {
                row.getCell(9).setCellValue(country.davCode);
            }
            order++;
            rowIndex++;

            row = sheet.getRow(rowIndex);

            for (int i = 0; i < 10; i++) {
                if (row.getCell(i) == null) {
                    row.createCell(i);
                }
            }

            row.getCell(0).setCellValue(order);
            row.getCell(1).setCellValue(assembler.name);
            row.getCell(2).setCellValue(assembler.address);
            row.getCell(3).setCellValue(assembler.phoneNumber);
            row.getCell(4).setCellValue(assembler.faxNumber);
            row.getCell(6).setCellValue(yes);

            if (country.davCode != null) {
                row.getCell(9).setCellValue(country.davCode);
            }
            order++;
            rowIndex++;
        }
        sheet.autoSizeColumn((short) 1);
        sheet.autoSizeColumn((short) 2);
    }

    private void fillIngredients(Sheet sheet, Notification notification) {
        int order = 1;
        int rowIndex = 4; // dav_form_4 => rowIndex =4
        Map<String, List<Ingredient>> listIngredients = new TreeMap<String, List<Ingredient>>();
        for (PresentationDetail detail : notification.presentationDetails) {
            if (detail.shadeName != null) {
                listIngredients.put(detail.shadeName, detail.ingredients);
            } else {
                listIngredients.put("", detail.ingredients);
            }
        }

        for (Map.Entry<String, List<Ingredient>> entry : listIngredients.entrySet()) {
            for (Ingredient ingredient : entry.getValue()) {
                Row row = sheet.getRow(rowIndex);
                if (row == null){
                    row = sheet.createRow(rowIndex);
                }
                for (int i = 0; i < 3; i++) {
                    if (row.getCell(i) == null) {
                        row.createCell(i);
                    }
                }
                row.getCell(0).setCellValue(order);
                row.getCell(1).setCellValue(ingredient.fullName);
                /*
                try {
                    if (ingredient.percentage != null) {
                        String percentage = ingredient.percentage.trim();
                        if (percentage.contains(",")) {
                            row.getCell(2).setCellValue(NumberFormat.getNumberInstance(Locale.FRANCE).parse(percentage).doubleValue());
                        } else if (percentage.contains(".")) {
                            row.getCell(2).setCellValue(Double.parseDouble(percentage));
                        } else {
                            row.getCell(2).setCellValue(percentage);
                        }
                    }
                } catch (ParseException ex) {

                }
                */
                row.getCell(2).setCellValue(ingredient.percentage);
                if (row.getCell(3) == null) {
                    row.createCell(3);
                }
                row.getCell(3).setCellValue(ingredient.note == null ? "" : ingredient.note);
                if (row.getCell(4) == null) {
                    row.createCell(4);
                }
                row.getCell(4).setCellValue(entry.getKey());

                order++;
                rowIndex++;
            }
        }
    }

    private void fillExporter(Sheet sheet, Notification notification) {
        Map<Long, ExporterFactory> factoryMap = new HashMap<Long, ExporterFactory>();
        int order = 1;
        int rowIndex = 4;
        for (PresentationDetail detail : notification.presentationDetails) {
            if (!factoryMap.containsKey(detail.manufacturerId)) {
                try {
                    ExporterFactory exporter = exporterFactoryService.get(detail.exporterId);
                    factoryMap.put(detail.exporterId, exporter);
                } catch (EntityNotFoundException e) {
                    ExporterFactory exporter = new ExporterFactory();
                    exporter.name = detail.exporterName;
                    exporter.address = detail.exporterAddress;
                    exporter.phoneNumber = detail.exporterPhoneNumber;
                    exporter.faxNumber = detail.exporterFaxNumber;
                    exporter.countryId = detail.exporterCountryId;
                    factoryMap.put(detail.exporterId, exporter);
                }

            }
        }
        for (Long id : factoryMap.keySet()) {
            Row row = sheet.getRow(rowIndex);
            ExporterFactory exporter = factoryMap.get(id);
            for (int i = 0; i < 5; i++) {
                if (row.getCell(i) == null) {
                    row.createCell(i);
                }
            }

            row.getCell(0).setCellValue(order);
            if (exporter.countryId != null) {
                Country country = countryService.get(exporter.countryId);
                if (row.getCell(4) == null) {
                    row.createCell(4);
                }
                if (country.davCode != null) {
                    row.getCell(4).setCellValue(country.davCode);
                }
            }
            order++;
            rowIndex++;
        }
        sheet.autoSizeColumn((short) 1);
        sheet.autoSizeColumn((short) 2);
    }

    public File exportNotitificationsToXls(String sheetName, List<Notification> notifications, List<String> fieldNames) throws IOException {
        Workbook wb = new HSSFWorkbook();
        File file = new File(fileDir + "/notifications" + System.currentTimeMillis() + ".xls");
        FileOutputStream output = new FileOutputStream(file);
        Sheet sheet1 = wb.createSheet(sheetName);
        sheet1.setDefaultColumnWidth(30);
        Row row = sheet1.createRow((short) 0);
        row.setHeightInPoints(2 * sheet1.getDefaultRowHeightInPoints());

        Cell cell;
        CellStyle headerStyle = headerStyle(wb);
        for (int i = 0; i < fieldNames.size(); i++) {
            cell = row.createCell(i);
            ExportedField field = ExportedField.valueOf(fieldNames.get(i));
            cell.setCellValue(field.displayName);
            cell.setCellStyle(headerStyle);
            sheet1.autoSizeColumn(i);
        }
        CellStyle dataCellStyle = dataCellStyle(wb);
        CellStyle dateCellStyle = dateCellStyle(wb);
        int rowNumber = 1;
        for (Notification notification : notifications) {
            if (notification != null) {
                if (notification.presentationDetails == null || notification.presentationDetails.size() == 0) {
                    writeRow(sheet1, notification, null, rowNumber, fieldNames, dataCellStyle, dateCellStyle);
                    rowNumber++;
                } else {
                    for (PresentationDetail detail : notification.presentationDetails) {
                        writeRow(sheet1, notification, detail, rowNumber, fieldNames, dataCellStyle, dateCellStyle);
                        rowNumber++;
                    }
                }
            }
        }
        wb.write(output);

        output.close();
        return file;
    }

    private static CellStyle headerStyle(Workbook wb) {
        CellStyle headerStyle = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        headerStyle.setWrapText(true);

        headerStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
        headerStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
        headerStyle.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        headerStyle.setBorderBottom(CellStyle.BORDER_THIN);
        headerStyle.setBorderLeft(CellStyle.BORDER_THIN);
        headerStyle.setBorderRight(CellStyle.BORDER_THIN);
        headerStyle.setBorderTop(CellStyle.BORDER_THIN);
        return headerStyle;
    }

    private static CellStyle dataCellStyle(Workbook wb) {
        CellStyle style = wb.createCellStyle();

        style.setWrapText(true);

        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setBorderTop(CellStyle.BORDER_THIN);
        return style;
    }

    private static CellStyle dateCellStyle(Workbook wb) {
        CellStyle style = wb.createCellStyle();
        CreationHelper createHelper = wb.getCreationHelper();


        style.setWrapText(true);

        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setDataFormat(createHelper.createDataFormat().getFormat("[$-en-US]d-mmm-yyyy;@"));
        return style;
    }

    private void writeRow(Sheet sheet, Notification notification, PresentationDetail detail, int rowNumber, List<String> fieldNames, CellStyle dataCellStyle, CellStyle dateCellStyle) {
        Row row = sheet.createRow(rowNumber);
        int cellNo = 0;
        for (String fieldName : fieldNames) {
            writeCell(row, fieldName, notification, detail, cellNo, dataCellStyle, dateCellStyle);
            cellNo++;
        }
    }

    private void writeCell(Row row, String fieldName, Notification notification, PresentationDetail detail, int cellNumber, CellStyle dataCellStyle, CellStyle dateCellStyle) {
        Cell cell = row.createCell(cellNumber);
        cell.setCellStyle(dataCellStyle);
        ExportedField field = ExportedField.valueOf(fieldName);
        switch (field) {
            case id:
                cell.setCellValue(notification.id);
                break;
            case presentationType:
                cell.setCellValue(notification.presentationType.name());
                break;
            case followerFullName:
                cell.setCellValue(notification.followerFullName);
                break;
            case followerEmail:
                cell.setCellValue(notification.followerEmail);
                break;
            case creatorFullName:
                cell.setCellValue(notification.creatorFullName);
                break;
            case creatorEmail:
                cell.setCellValue(notification.creatorEmail);
                break;
            case status:
                cell.setCellValue(notification.status.name());
                break;
            case brand:
                cell.setCellValue(notification.brandName);
                break;
            case productRange:
                cell.setCellValue(notification.productRangeName);
                break;
            case productName:
                cell.setCellValue(notification.productName);
                break;
            case notiType:
                cell.setCellValue(notification.notiType);
                break;
            case productType:
                StringBuilder sb = new StringBuilder("");
                if (notification.productTypeIds != null && notification.productTypeIds.size() > 0) {
                    for (long productTypeId : notification.productTypeIds) {
                        ProductType productType = productTypeService.get(productTypeId);
                        sb.append(productType.description).append("\n");
                    }
                }
                cell.setCellValue(sb.toString());
                break;
            case aw:
                cell.setCellValue(getCellValueForFiles(notification.awFiles));
                break;
            case launchForm:
                cell.setCellValue(getCellValueForFiles(notification.launchFormFiles));
                break;
            case launchTime:
                if (notification.launchTime != null) {
                    cell.setCellValue(notification.launchTime);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case shipmentRequestTime:
                if (notification.shipmentRequestTime != null) {
                    cell.setCellValue(notification.shipmentRequestTime);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case assigneeFullName:
                cell.setCellValue(notification.assigneeFullName);
                break;
            case assigneeEmail:
                cell.setCellValue(notification.assigneeEmail);
                break;
            case validatedTime:
                if (notification.validatedTime != null) {
                    cell.setCellValue(notification.validatedTime);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case cfsRequestingDate:
                if (notification.cfsRequestingDate != null) {
                    cell.setCellValue(notification.cfsRequestingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case cfsAvailable:
                if (notification.cfsAvailable != null) {
                    cell.setCellValue(notification.cfsAvailable);
                }
                break;
            case cfsReceivingDate:
                if (notification.cfsReceivingDate != null) {
                    cell.setCellValue(notification.cfsReceivingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case cfs:
                cell.setCellValue(getCellValueForFiles(notification.cfsFiles));
                break;
            case inciRequestingDate:
                if (notification.inciRequestingDate != null) {
                    cell.setCellValue(notification.inciRequestingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case inciAvailable:
                if (notification.inciAvailable != null) {
                    cell.setCellValue(notification.inciAvailable);
                }
                break;
            case inciReceivingDate:
                if (notification.inciReceivingDate != null) {
                    cell.setCellValue(notification.inciReceivingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case inci:
                cell.setCellValue(getCellValueForFiles(notification.inciFiles));
                break;
            case davNotificationNumber:
                cell.setCellValue(notification.davNotificationNumber);
                break;
            case davReceivingDate:
                if (notification.davReceivingDate != null) {
                    cell.setCellValue(notification.davReceivingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case davExpiringDate:
                if (notification.davExpiringDate != null) {
                    cell.setCellValue(notification.davExpiringDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case dav:
                cell.setCellValue(getCellValueForFiles(notification.davFiles));
                break;
            case productCode:
                if (detail != null) {
                    cell.setCellValue(detail.productCode);
                } else {
                    cell.setCellValue("");
                }
                break;
            case barCode:
                if (detail != null) {
                    cell.setCellValue(detail.barCode);
                } else {
                    cell.setCellValue("");
                }
                break;
            case formulaNumber:
                if (detail != null) {
                    cell.setCellValue(detail.formulaNumber);
                } else {
                    cell.setCellValue("");
                }
                break;
            case filCode:
                if (detail != null) {
                    cell.setCellValue(detail.filCode);
                } else {
                    cell.setCellValue("");
                }
                break;
            case compositeNumber:
                if (detail != null) {
                    cell.setCellValue(detail.compositeNumber);
                } else {
                    cell.setCellValue("");
                }
                break;
            case netWeight:
                if (detail != null) {
                    cell.setCellValue(detail.netWeight);
                } else {
                    cell.setCellValue("");
                }
                break;
            case volume:
                if (detail != null) {
                    cell.setCellValue(detail.volume);
                } else {
                    cell.setCellValue("");
                }
                break;
            case commonShadeName:
                if (notification.presentationType == Presentation.SINGLE) {
                    if (detail != null) {
                        cell.setCellValue(detail.shadeName);
                    } else {
                        cell.setCellValue("");
                    }
                } else {
                    if (detail != null) {
                        cell.setCellValue(detail.commonShadeName);
                    } else {
                        cell.setCellValue("");
                    }
                }
                break;
            case shadeName:
                if (notification.presentationType == Presentation.SINGLE || notification.presentationType == Presentation.COMBINATION || notification.presentationType == Presentation.OTHER) {

                } else {
                    if (detail != null) {
                        cell.setCellValue(detail.shadeName);
                    } else {
                        cell.setCellValue("");
                    }
                }
                break;
            case shadeCode:
                if (detail != null) {
                    cell.setCellValue(detail.shadeCode);
                } else {
                    cell.setCellValue("");
                }
                break;
            case intendedUse:
                if (notification.intendedUse != null) {
                    cell.setCellValue(notification.intendedUse);
                } else if (detail != null && detail.individualIntendedUse != null) {
                    cell.setCellValue(detail.individualIntendedUse);
                } else {
                    cell.setCellValue("");
                }
                break;
            case manufacturerCountry:
                if (detail != null) {
                    cell.setCellValue(detail.manufacturerCountryName);
                } else {
                    cell.setCellValue("");
                }
                break;
            case manufacturer:
                if (detail != null) {
                    cell.setCellValue(detail.manufacturerName);
                } else {
                    cell.setCellValue("");
                }
                break;
            case manufacturerAddress:
                if (detail != null) {
                    cell.setCellValue(detail.manufacturerAddress);
                } else {
                    cell.setCellValue("");
                }
                break;
            case assemblerCountry:
                if (detail != null) {
                    cell.setCellValue(detail.assemblerCountryName);
                } else {
                    cell.setCellValue("");
                }
                break;
            case assembler:
                if (detail != null) {
                    cell.setCellValue(detail.assemblerName);
                } else {
                    cell.setCellValue("");
                }
                break;
            case assemblerType:
                if (detail != null) {
                    cell.setCellValue(detail.assemblerType.name());
                } else {
                    cell.setCellValue("");
                }
                break;
            case exporterCountry:
                if (detail != null) {
                    cell.setCellValue(detail.exporterCountryName);
                } else {
                    cell.setCellValue("");
                }
                break;
            case exporter:
                if (detail != null) {
                    cell.setCellValue(detail.exporterName);
                } else {
                    cell.setCellValue("");
                }
                break;
            case exporterAddress:
                if (detail != null) {
                    cell.setCellValue(detail.exporterAddress);
                } else {
                    cell.setCellValue("");
                }
                break;
            case otherPresentationType:
                cell.setCellValue(notification.otherPresentationType == null ? "" : notification.otherPresentationType);
                break;
            case productNameVn:
                cell.setCellValue(notification.productNameVn == null ? "" : notification.productNameVn);
                break;
            case submittingDate:
                if (notification.submittingDate != null) {
                    cell.setCellValue(notification.submittingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case revisingDate:
                if (notification.revisingDate != null) {
                    cell.setCellValue(notification.revisingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case davRequestingDate:
                if (notification.davRequestingDate != null) {
                    cell.setCellValue(notification.davRequestingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case davAvailable:
                if (notification.davAvailable != null) {
                    cell.setCellValue(notification.davAvailable);
                }
                break;
            case discontinued:
                if (detail != null && detail.discontinued != null && detail.discontinued == 1) {
                    cell.setCellValue("x");
                } else {
                    cell.setCellValue("");
                }
                break;
            case intentDiscontinuedDate:
                if (detail != null && detail.intentDiscontinuedDate != null) {
                    cell.setCellValue(detail.intentDiscontinuedDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case officialDiscontinuedDate:
                if (detail != null && detail.officialDiscontinuedDate != null) {
                    cell.setCellValue(detail.officialDiscontinuedDate);
                    cell.setCellStyle(dateCellStyle);
                }
            case hasAdvertising:
                if (notification.advertising != null && !notification.advertising.isEmpty()) {
                    cell.setCellValue("x");
                } else {
                    cell.setCellValue("");
                }
                break;
            case hasLabel:
                if (notification.hasLabel != null && notification.hasLabel) {
                    cell.setCellValue("x");
                } else {
                    cell.setCellValue("");
                }
                break;
            case individualProductName:
                if (notification.presentationType == Presentation.COMBINATION || notification.presentationType == Presentation.OTHER) {
                    cell.setCellValue(detail.shadeName == null ? "" : detail.shadeName);
                }
                break;
            default:
                break;
        }
    }

    private CellStyle cellStyle(Workbook wb) {
        CellStyle style = wb.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_LEFT);
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        Font font = wb.createFont();
        font.setFontName("Times New Roman");
        style.setFont(font);
        return style;
    }

    private String getCellValueForFiles(Map<String, String> filesMap) {
        StringBuilder sb = new StringBuilder("");
        if (filesMap != null && filesMap.size() > 0) {
            for (String fileId : filesMap.keySet()) {
                sb.append(downloadUrl).append(fileId).append("\n");
            }
        }
        return sb.toString();
    }

    private String getDateStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MMM-yyyy");
        return sdf.format(date);
    }

    public static void removeRow(Sheet sheet, int rowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        if (rowIndex >= 0 && rowIndex < lastRowNum) {
            sheet.shiftRows(rowIndex + 1, lastRowNum, -1);
        }
        if (rowIndex == lastRowNum) {
            Row removingRow = sheet.getRow(rowIndex);
            if (removingRow != null) {
                sheet.removeRow(removingRow);
            }
        }
    }

    public File exportLabelBundle(Label label, List<Notification> listNoti) throws IOException {
        File file = new File(fileDir + "/export_" + label.id + System.currentTimeMillis() + ".xlsx");
        FileOutputStream output = new FileOutputStream(file);

        InputStream is = getClass().getClassLoader().getResourceAsStream("templates/label_bundle.xlsx");

        Workbook wb = new XSSFWorkbook(is);
        Sheet sheet = wb.getSheetAt(0);
        for (int i = 0; i < listNoti.size(); i++) {

            Notification noti = listNoti.get(i);
            Row row2 = sheet.getRow(2);
            row2.getCell(i + 2).setCellValue(noti.id);

            Row row3 = sheet.getRow(3);
            String brandName = noti.brandName;
            row3.getCell(i + 2).setCellValue(brandName);

            Row row4 = sheet.getRow(4);
            String productName = findStringByRequestNumber(label.productName, noti.id.toString());
            row4.getCell(i + 2).setCellValue(productName);

            StringBuilder skus = new StringBuilder();
            String intendedUses = "";
            for (PresentationDetail presentationDetail : noti.presentationDetails) {
                if (label.presentationDetailIds != null)
                    for (String presentationId : label.presentationDetailIds) {
                        if (presentationDetail.id.toString().equals(presentationId)) {
                            skus.append(!presentationDetail.shadeName.isEmpty() ? presentationDetail.shadeName : "").append(" - ");
                            skus.append(!presentationDetail.formulaNumber.isEmpty() ? presentationDetail.formulaNumber : "").append(" - ");
                            skus.append(!presentationDetail.volume.isEmpty() && !presentationDetail.netWeight.isEmpty() ? presentationDetail.volume + "/" + presentationDetail.netWeight :
                                    !presentationDetail.volume.isEmpty() ? presentationDetail.volume : !presentationDetail.netWeight.isEmpty() ? presentationDetail.netWeight : "").append(" - ");
                            skus.append(!presentationDetail.productCode.isEmpty() ? presentationDetail.productCode : "");
                            skus.append("\n");
                        }
                    }
            }

            Row row5 = sheet.getRow(5);
            row5.getCell(i + 2).setCellValue(skus.length() > 0 ? skus.toString() : "");

//            Row row6 = sheet.getRow(6);
//            row6.getCell(i+2).setCellValue(code.length() > 0 ? code.substring(0,code.length()-1) : "");

            Row row7 = sheet.getRow(6);
            String warning = "";
            if (!Strings.isNullOrEmpty(label.alarm))
                warning = findStringByRequestNumber(label.alarm, noti.id.toString());
            row7.getCell(i + 2).setCellValue(warning);

            Row row8 = sheet.getRow(7);
            if (!Strings.isNullOrEmpty(label.uses))
                intendedUses = findStringByRequestNumber(label.uses, noti.id.toString());
            row8.getCell(i + 2).setCellValue(intendedUses);

            Row row9 = sheet.getRow(8);
            String hdsd = "";
            if (!Strings.isNullOrEmpty(label.userManual))
                hdsd = findStringByRequestNumber(label.userManual, noti.id.toString());
            row9.getCell(i + 2).setCellValue(hdsd);

            Row row10 = sheet.getRow(9);
            if (!Strings.isNullOrEmpty(label.ingredients)) {
                row10.getCell(i + 2).setCellValue(findStringByRequestNumber(label.ingredients, noti.id.toString()));
            } else row10.getCell(i + 2).setCellValue("");

            Row row11 = sheet.getRow(10);
            if (!Strings.isNullOrEmpty(label.colorIngredients)) {
                row11.getCell(i + 2).setCellValue(findStringByRequestNumber(label.colorIngredients, noti.id.toString()));
            } else row11.getCell(i + 2).setCellValue("");

            Row row12 = sheet.getRow(11);
            if (!Strings.isNullOrEmpty(label.baseIngredients)) {
                row12.getCell(i + 2).setCellValue(findStringByRequestNumber(label.baseIngredients, noti.id.toString()));
            } else row12.getCell(i + 2).setCellValue("");

            Row row13 = sheet.getRow(12);
            if (!Strings.isNullOrEmpty(label.preservation)) {
                row13.getCell(i + 2).setCellValue(findStringByRequestNumber(label.preservation, noti.id.toString()));
            } else row12.getCell(i + 2).setCellValue("");

            Row row14 = sheet.getRow(13);
            if (!Strings.isNullOrEmpty(label.other)) {
                row14.getCell(i + 2).setCellValue(findStringByRequestNumber(label.other, noti.id.toString()));
            } else row14.getCell(i + 2).setCellValue("");

            Row row15 = sheet.getRow(14);
            if (!Strings.isNullOrEmpty(label.netVolume)) {
                row15.getCell(i + 2).setCellValue(findStringByRequestNumber(label.netVolume, noti.id.toString()));
            } else row15.getCell(i + 2).setCellValue("");

            Row row16 = sheet.getRow(15);
            if (!Strings.isNullOrEmpty(label.sapCode)) {
                row16.getCell(i + 2).setCellValue(findStringByRequestNumber(label.sapCode, noti.id.toString()));
            } else row16.getCell(i + 2).setCellValue("");
        }

        int countRemovedRow = 0;
        //Set bundle
        Row row = sheet.getRow(18 - countRemovedRow);
        row.getCell(0).setCellValue(1 - countRemovedRow);
        row.getCell(2).setCellValue(label.bundle);
        if (StringUtils.isEmpty(label.bundle)) {
            removeRow(sheet, (16 - countRemovedRow));
            countRemovedRow++;
        }

        //Set importer
        row = sheet.getRow(19 - countRemovedRow);
        row.getCell(0).setCellValue(2 - countRemovedRow);
        row.getCell(2).setCellValue(label.importer);
        if (StringUtils.isEmpty(label.importer)) {
            removeRow(sheet, (19 - countRemovedRow));
            countRemovedRow++;
        }

        //Set owner
        row = sheet.getRow(20 - countRemovedRow);
        row.getCell(0).setCellValue(2 - countRemovedRow);
        row.getCell(2).setCellValue(label.owner);
        if (StringUtils.isEmpty(label.owner)) {
            removeRow(sheet, (20 - countRemovedRow));
            countRemovedRow++;
        }

        //Set distributor
        row = sheet.getRow(21 - countRemovedRow);
        row.getCell(0).setCellValue(3 - countRemovedRow);
        row.getCell(2).setCellValue(label.distributor);
        if (StringUtils.isEmpty(label.distributor)) {
            removeRow(sheet, (21 - countRemovedRow));
            countRemovedRow++;
        }

        //Set lot number
        row = sheet.getRow(22 - countRemovedRow);
        row.getCell(0).setCellValue(4 - countRemovedRow);
        row.getCell(2).setCellValue(label.lotNumber);
        if (StringUtils.isEmpty(label.lotNumber)) {
            removeRow(sheet, (22 - countRemovedRow));
            countRemovedRow++;
        }

        //Set date of manufacturing
        row = sheet.getRow(23 - countRemovedRow);
        row.getCell(0).setCellValue(5 - countRemovedRow);
        row.getCell(2).setCellValue(label.manufacturingDate);
        if (StringUtils.isEmpty(label.manufacturingDate)) {
            removeRow(sheet, (23 - countRemovedRow));
            countRemovedRow++;
        }

        //Set expiration date
        row = sheet.getRow(24 - countRemovedRow);
        row.getCell(0).setCellValue(6 - countRemovedRow);
        row.getCell(2).setCellValue(label.expirationDate);
        if (StringUtils.isEmpty(label.expirationDate)) {
            removeRow(sheet, (24 - countRemovedRow));
            countRemovedRow++;
        }

        //Set hotline
        row = sheet.getRow(25 - countRemovedRow);
        row.getCell(0).setCellValue(7 - countRemovedRow);
        row.getCell(2).setCellValue(label.hotline);
        if (StringUtils.isEmpty(label.hotline)) {
            removeRow(sheet, (25 - countRemovedRow));
            countRemovedRow++;
        }

        //Set dav notification number
        row = sheet.getRow(26 - countRemovedRow);
        row.getCell(0).setCellValue(8 - countRemovedRow);
        row.getCell(2).setCellValue(label.davNotificationNumber);
        if (StringUtils.isEmpty(label.davNotificationNumber)) {
            removeRow(sheet, (26 - countRemovedRow));
            countRemovedRow++;
        }

        //Set manufacturer
        row = sheet.getRow(27 - countRemovedRow);
        row.getCell(0).setCellValue(9 - countRemovedRow);
        row.getCell(2).setCellValue(label.manufacturer);
        if (StringUtils.isEmpty(label.manufacturer)) {
            removeRow(sheet, (27 - countRemovedRow));
            countRemovedRow++;
        }

        //Set bar code
        row = sheet.getRow(28 - countRemovedRow);
        row.getCell(0).setCellValue(10 - countRemovedRow);
        row.getCell(2).setCellValue(label.barCode);
        if (StringUtils.isEmpty(label.barCode.trim())) {
            removeRow(sheet, (28 - countRemovedRow));
            countRemovedRow++;
        }

        wb.write(output);
        output.close();
        return file;
    }

    public static String findStringByRequestNumber(String input, String number) {
        // Khởi tạo StringBuilder để xây dựng chuỗi kết quả
        StringBuilder resultBuilder = new StringBuilder();

        // Tách chuỗi thành các dòng
        String[] lines = input.split("Request No");

        // Duyệt qua từng dòng
        for (String line : lines) {
            // Tách số và nội dung chuỗi
            String[] subParts = line.split(":", 2);
            if (subParts.length == 2 && subParts[0].contains(number)) {
                // Thêm nội dung chuỗi vào StringBuilder
                resultBuilder.append(subParts[1]);
            }
        }

        // Trả về chuỗi kết quả, loại bỏ ký tự "\n" ở cuối nếu có
        return resultBuilder.length() > 0 ? resultBuilder.toString() : "";
    }

    public File exportLabelSingle(Label label, List<Notification> listNoti) throws IOException {
        File file = new File(fileDir + "/export_" + label.id + System.currentTimeMillis() + ".xlsx");
        FileOutputStream output = new FileOutputStream(file);

        InputStream is = getClass().getClassLoader().getResourceAsStream("templates/label.xlsx");

        int countRemovedRow = 0;
        Workbook wb = new XSSFWorkbook(is);
        Sheet sheet = wb.getSheetAt(0);

        //Set product name
        Row row = sheet.getRow(1 - countRemovedRow);
        String productName = findStringByRequestNumber(label.productName, listNoti.get(0).id.toString());
        String productNameReal = productName.length() == 0 ? label.productName : productName;
        row.getCell(0).setCellValue(1 - countRemovedRow);
        row.getCell(2).setCellValue(productNameReal);
        if (StringUtils.isEmpty(productNameReal)) {
            removeRow(sheet, (1 - countRemovedRow));
            countRemovedRow++;
        }

        //Set user manual
        row = sheet.getRow(2 - countRemovedRow);
        String userManual = findStringByRequestNumber(label.userManual, listNoti.get(0).id.toString());
        String userManualReal = userManual.length() == 0 ? label.userManual : userManual;
        row.getCell(0).setCellValue(2 - countRemovedRow);
        row.getCell(2).setCellValue(userManualReal);
        if (StringUtils.isEmpty(userManualReal)) {
            removeRow(sheet, (2 - countRemovedRow));
            countRemovedRow++;
        }

        //Set importer
        row = sheet.getRow(3 - countRemovedRow);
        row.getCell(0).setCellValue(3 - countRemovedRow);
        row.getCell(2).setCellValue(label.importer);
        if (StringUtils.isEmpty(label.importer)) {
            removeRow(sheet, (3 - countRemovedRow));
            countRemovedRow++;
        }

        //Set owner
        row = sheet.getRow(4 - countRemovedRow);
        row.getCell(0).setCellValue(4 - countRemovedRow);
        row.getCell(2).setCellValue(label.owner);
        if (StringUtils.isEmpty(label.owner)) {
            removeRow(sheet, (4 - countRemovedRow));
            countRemovedRow++;
        }

        //Set distributor
        row = sheet.getRow(5 - countRemovedRow);
        row.getCell(0).setCellValue(5 - countRemovedRow);
        row.getCell(2).setCellValue(label.distributor);
        if (StringUtils.isEmpty(label.distributor)) {
            removeRow(sheet, (5 - countRemovedRow));
            countRemovedRow++;
        }

        //Set alarm
        row = sheet.getRow(6 - countRemovedRow);
        String alarm = findStringByRequestNumber(label.alarm, listNoti.get(0).id.toString());
        String alarmReal = alarm.length() == 0 ? label.alarm : alarm;
        row.getCell(0).setCellValue(6 - countRemovedRow);
        row.getCell(2).setCellValue(alarmReal);
        if (StringUtils.isEmpty(alarmReal)) {
            removeRow(sheet, (6 - countRemovedRow));
            countRemovedRow++;
        }

        //Set lot number
        row = sheet.getRow(7 - countRemovedRow);
        row.getCell(0).setCellValue(7 - countRemovedRow);
        row.getCell(2).setCellValue(label.lotNumber);
        if (StringUtils.isEmpty(label.lotNumber)) {
            removeRow(sheet, (7 - countRemovedRow));
            countRemovedRow++;
        }

        //Set date of manufacturing
        row = sheet.getRow(8 - countRemovedRow);
        row.getCell(0).setCellValue(8 - countRemovedRow);
        row.getCell(2).setCellValue(label.manufacturingDate);
        if (StringUtils.isEmpty(label.manufacturingDate)) {
            removeRow(sheet, (8 - countRemovedRow));
            countRemovedRow++;
        }

        //Set expiration date
        row = sheet.getRow(9 - countRemovedRow);
        row.getCell(0).setCellValue(9 - countRemovedRow);
        row.getCell(2).setCellValue(label.expirationDate);
        if (StringUtils.isEmpty(label.expirationDate)) {
            removeRow(sheet, (9 - countRemovedRow));
            countRemovedRow++;
        }

        //Set hotline
        row = sheet.getRow(10 - countRemovedRow);
        row.getCell(0).setCellValue(10 - countRemovedRow);
        row.getCell(2).setCellValue(label.hotline);
        if (StringUtils.isEmpty(label.hotline)) {
            removeRow(sheet, (10 - countRemovedRow));
            countRemovedRow++;
        }

        //Set ingredients
        row = sheet.getRow(11 - countRemovedRow);
        String ingredients = findStringByRequestNumber(label.ingredients, listNoti.get(0).id.toString());
        String ingredientsRea = ingredients.length() == 0 ? label.ingredients : ingredients;
        row.getCell(0).setCellValue(11 - countRemovedRow);
        row.getCell(2).setCellValue(ingredientsRea);
        if (StringUtils.isEmpty(ingredientsRea)) {
            removeRow(sheet, (11 - countRemovedRow));
            countRemovedRow++;
        }

        //Set base ingredients
        row = sheet.getRow(12 - countRemovedRow);
        String baseIngredients = findStringByRequestNumber(label.baseIngredients, listNoti.get(0).id.toString());
        String baseIngredientsReal = baseIngredients.length() == 0 ? label.baseIngredients : baseIngredients;
        row.getCell(0).setCellValue(12 - countRemovedRow);
        row.getCell(2).setCellValue(baseIngredientsReal);
        if (StringUtils.isEmpty(baseIngredientsReal)) {
            removeRow(sheet, (12 - countRemovedRow));
            countRemovedRow++;
        }

        //Set color ingredients
        row = sheet.getRow(13 - countRemovedRow);
        String color = findStringByRequestNumber(label.colorIngredients, listNoti.get(0).id.toString());
        String colorReal = color.length() == 0 ? label.colorIngredients : color;
        row.getCell(0).setCellValue(13 - countRemovedRow);
        row.getCell(2).setCellValue(colorReal);
        if (StringUtils.isEmpty(colorReal)) {
            removeRow(sheet, (13 - countRemovedRow));
            countRemovedRow++;
        }

        //Set dav notification number
        row = sheet.getRow(14 - countRemovedRow);
        row.getCell(0).setCellValue(14 - countRemovedRow);
        row.getCell(2).setCellValue(label.davNotificationNumber);
        if (StringUtils.isEmpty(label.davNotificationNumber)) {
            removeRow(sheet, (14 - countRemovedRow));
            countRemovedRow++;
        }

        //Set manufacturer
        row = sheet.getRow(15 - countRemovedRow);
        row.getCell(0).setCellValue(15 - countRemovedRow);
        row.getCell(2).setCellValue(label.manufacturer);
        if (StringUtils.isEmpty(label.manufacturer)) {
            removeRow(sheet, (15 - countRemovedRow));
            countRemovedRow++;
        }

        //Set preservation
        row = sheet.getRow(16 - countRemovedRow);
        String preservation = findStringByRequestNumber(label.preservation, listNoti.get(0).id.toString());
        String preservationReal = preservation.length() == 0 ? label.preservation : preservation;
        row.getCell(0).setCellValue(16 - countRemovedRow);
        row.getCell(2).setCellValue(preservationReal);
        if (StringUtils.isEmpty(preservationReal)) {
            removeRow(sheet, (16 - countRemovedRow));
            countRemovedRow++;
        }

        //Set net volume
        row = sheet.getRow(17 - countRemovedRow);
        String netVolumn = findStringByRequestNumber(label.netVolume, listNoti.get(0).id.toString());
        String netVolumnReal = netVolumn.length() == 0 ? label.netVolume : netVolumn;
//        for (PresentationDetail presentationDetail : listNoti.get(0).presentationDetails) {
//            for (String presentationId : label.presentationDetailIds) {
//                if (presentationDetail.id.toString().equals(presentationId)) {
//
//                    netVolumn.append(!presentationDetail.volume.isEmpty() && !presentationDetail.netWeight.isEmpty() ? presentationDetail.volume+"/"+presentationDetail.netWeight:
//                            !presentationDetail.volume.isEmpty() ? presentationDetail.volume : !presentationDetail.netWeight.isEmpty() ?  presentationDetail.netWeight: "").append(" - ");
////                    skus.append(!presentationDetail.productCode.isEmpty() ? presentationDetail.productCode :"").append(" - ");
//                    netVolumn.append(",");
//                }
//            }
//        }
        row.getCell(0).setCellValue(17 - countRemovedRow);
        row.getCell(2).setCellValue(netVolumnReal);
        if (StringUtils.isEmpty(netVolumnReal)) {
            removeRow(sheet, (17 - countRemovedRow));
            countRemovedRow++;
        }

        //Set uses
        row = sheet.getRow(18 - countRemovedRow);
        String uses = findStringByRequestNumber(label.uses, listNoti.get(0).id.toString());
        String usesReal = uses.length() == 0 ? label.uses : uses;
        row.getCell(0).setCellValue(18 - countRemovedRow);
        row.getCell(2).setCellValue(usesReal);
        if (StringUtils.isEmpty(usesReal)) {
            removeRow(sheet, (18 - countRemovedRow));
            countRemovedRow++;
        }


        //Set bar code
        row = sheet.getRow(19 - countRemovedRow);
        row.getCell(0).setCellValue(19 - countRemovedRow);
        row.getCell(2).setCellValue(label.barCode);
        if (StringUtils.isEmpty(label.barCode.trim())) {
            removeRow(sheet, (19 - countRemovedRow));
            countRemovedRow++;
        }

        //Set sap code
        row = sheet.getRow(20 - countRemovedRow);
        String sapCode = findStringByRequestNumber(label.sapCode, listNoti.get(0).id.toString());
        String sapCodeReal = sapCode.length() == 0 ? label.sapCode : sapCode;
        row.getCell(0).setCellValue(20 - countRemovedRow);
        row.getCell(2).setCellValue(sapCodeReal);
        if (StringUtils.isEmpty(sapCodeReal)) {
            removeRow(sheet, (20 - countRemovedRow));
            countRemovedRow++;
        }

        //Set other
        row = sheet.getRow(21 - countRemovedRow);
        String other = findStringByRequestNumber(label.other, listNoti.get(0).id.toString());
        String otherreal = other.length() == 0 ? label.other : other;
        row.getCell(0).setCellValue(21 - countRemovedRow);
        row.getCell(2).setCellValue(otherreal);
        if (StringUtils.isEmpty(otherreal)) {
            removeRow(sheet, (21 - countRemovedRow));
            countRemovedRow++;
        }

        wb.write(output);
        output.close();
        return file;
    }


    public File exportLabelsToXls(String sheetName, List<Label> labels, List<String> fieldNames) throws IOException {
        Workbook wb = new HSSFWorkbook();
        File file = new File(fileDir + "/labels" + System.currentTimeMillis() + ".xls");
        FileOutputStream output = new FileOutputStream(file);
        Sheet sheet1 = wb.createSheet(sheetName);
        sheet1.setDefaultColumnWidth(30);
        Row row = sheet1.createRow((short) 0);
        row.setHeightInPoints(2 * sheet1.getDefaultRowHeightInPoints());

        Cell cell;
        CellStyle headerStyle = headerStyle(wb);
        for (int i = 0; i < fieldNames.size(); i++) {
            cell = row.createCell(i);
            LabelExportedField field = LabelExportedField.valueOf(fieldNames.get(i));
            cell.setCellValue(field.displayName);
            cell.setCellStyle(headerStyle);
            sheet1.autoSizeColumn(i);
        }
        CellStyle dataCellStyle = dataCellStyle(wb);
        CellStyle dateCellStyle = dateCellStyle(wb);
        int rowNumber = 1;
        for (Label label : labels) {
            if (label != null) {
                writeLabelRow(sheet1, label, rowNumber, fieldNames, dataCellStyle, dateCellStyle);
                rowNumber++;
            }
        }
        wb.write(output);

        output.close();
        return file;
    }

    private void writeLabelRow(Sheet sheet, Label label, int rowNumber, List<String> fieldNames, CellStyle dataCellStyle, CellStyle dateCellStyle) {
        Row row = sheet.createRow(rowNumber);
        int cellNo = 0;
        for (String fieldName : fieldNames) {
            writeLabelCell(row, fieldName, label, cellNo, dataCellStyle, dateCellStyle);
            cellNo++;
        }
    }

    private void writeLabelCell(Row row, String fieldName, Label label, int cellNumber, CellStyle dataCellStyle, CellStyle dateCellStyle) {
        Cell cell = row.createCell(cellNumber);
        cell.setCellStyle(dataCellStyle);
        LabelExportedField field = LabelExportedField.valueOf(fieldName);
        switch (field) {
            case id:
                cell.setCellValue(label.id);
                break;
            case notificationId:
//                cell.setCellValue(label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", ")));
                cell.setCellValue(getCellValueForRequests(label.notificationIds.stream()
                        .map(Long::intValue)
                        .collect(Collectors.toList())));

                break;
            case notificationIds:
//                cell.setCellValue(label.notificationIds.stream().map(Objects::toString).collect(Collectors.joining(", ")));
                cell.setCellValue(getCellValueForRequests(label.notificationIds.stream()
                        .map(Long::intValue)
                        .collect(Collectors.toList())));
                break;
            case barCode:
                cell.setCellValue(label.barCode);
                break;
            case sapCode:
                cell.setCellValue(label.sapCode);
                break;
            case status:
                cell.setCellValue(label.status.name());
                break;
            case submittedDate:
                if (label.submittedDate != null) {
                    cell.setCellValue(label.submittedDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case logisticValidatedDate:
                if (label.logisticValidatedDate != null) {
                    cell.setCellValue(label.logisticValidatedDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case sciValidatedDate:
                if (label.sciValidatedDate != null) {
                    cell.setCellValue(label.sciValidatedDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case brandName:
                cell.setCellValue(label.brandName);
                break;
            case productName:
                cell.setCellValue(label.productName);
                break;
            case preservation:
                cell.setCellValue(label.preservation);
                break;
            case alarm:
                cell.setCellValue(label.alarm);
                break;
            case importer:
                cell.setCellValue(label.importer);
                break;
            case owner:
                cell.setCellValue(label.owner);
                break;
            case uses:
                cell.setCellValue(label.uses);
                break;
            case followerEmail:
                cell.setCellValue(label.followerEmail);
                break;
            case creatorEmail:
                cell.setCellValue(label.creatorEmail);
                break;
            case assigneeEmail:
                cell.setCellValue(label.assigneeEmail);
                break;
            case userManual:
                cell.setCellValue(label.userManual);
                break;
            case expirationDate:
                cell.setCellValue(label.expirationDate);
                break;
            case manufacturingDate:
                cell.setCellValue(label.manufacturingDate);
                break;
            case followerFullName:
                cell.setCellValue(label.followerFullName);
                break;
            case creatorFullName:
                cell.setCellValue(label.creatorFullName);
                break;
            case assigneeFullName:
                cell.setCellValue(label.assigneeFullName);
                break;
            case distributor:
                cell.setCellValue(label.distributor);
                break;
            case manufacturer:
                cell.setCellValue(label.manufacturer);
                break;
            case davNotificationNumber:
                cell.setCellValue(label.davNotificationNumber);
                break;
            case hotline:
                cell.setCellValue(label.hotline);
                break;
            case lotNumber:
                cell.setCellValue(label.lotNumber);
                break;
            case ingredients:
                cell.setCellValue(label.ingredients);
                break;
            case colorIngredients:
                cell.setCellValue(label.colorIngredients);
                break;
            case baseIngredients:
                cell.setCellValue(label.baseIngredients);
                break;
            case netVolume:
                cell.setCellValue(label.netVolume);
                break;
            case other:
                cell.setCellValue(label.other);
                break;
            default:
                break;
        }
    }

    public File exportAdvertisingsToXls(String sheetName, List<Advertising> advertisings, List<String> fieldNames) throws IOException {
        Workbook wb = new HSSFWorkbook();
        File file = new File(fileDir + "/advertisings" + System.currentTimeMillis() + ".xls");
        FileOutputStream output = new FileOutputStream(file);
        Sheet sheet1 = wb.createSheet(sheetName);
        sheet1.setDefaultColumnWidth(30);
        Row row = sheet1.createRow((short) 0);
        row.setHeightInPoints(2 * sheet1.getDefaultRowHeightInPoints());

        Cell cell;
        CellStyle headerStyle = headerStyle(wb);
        for (int i = 0; i < fieldNames.size(); i++) {
            cell = row.createCell(i);
            AdvertisingExportedField field = AdvertisingExportedField.valueOf(fieldNames.get(i));
            cell.setCellValue(field.displayName);
            cell.setCellStyle(headerStyle);
            sheet1.autoSizeColumn(i);
        }
        CellStyle dataCellStyle = dataCellStyle(wb);
        CellStyle dateCellStyle = dateCellStyle(wb);
        int rowNumber = 1;
        for (Advertising advertising : advertisings) {
            if (advertising != null) {
                writeAdvertisingRow(sheet1, advertising, rowNumber, fieldNames, dataCellStyle, dateCellStyle);
                rowNumber++;
            }
        }
        wb.write(output);

        output.close();
        return file;
    }

    public File exportCraRequestsToXls(String sheetName, List<CraRequest> craRequests, List<String> fieldNames) throws IOException {
        Workbook wb = new HSSFWorkbook();
        File file = new File(fileDir + "/cra_requests" + System.currentTimeMillis() + ".xls");
        FileOutputStream output = new FileOutputStream(file);
        Sheet sheet1 = wb.createSheet(sheetName);
        sheet1.setDefaultColumnWidth(30);
        Row row = sheet1.createRow((short) 0);
        row.setHeightInPoints(2 * sheet1.getDefaultRowHeightInPoints());

        Cell cell;
        CellStyle headerStyle = headerStyle(wb);
        for (int i = 0; i < fieldNames.size(); i++) {
            cell = row.createCell(i);
            CraRequestExportedField field = CraRequestExportedField.valueOf(fieldNames.get(i));
            cell.setCellValue(field.displayName);
            cell.setCellStyle(headerStyle);
            sheet1.autoSizeColumn(i);
        }
        CellStyle dataCellStyle = dataCellStyle(wb);
        CellStyle dateCellStyle = dateCellStyle(wb);
        int rowNumber = 1;
        for (CraRequest craRequest : craRequests) {
            if (craRequest != null) {
                writeCraRequestRow(sheet1, craRequest, rowNumber, fieldNames, dataCellStyle, dateCellStyle);
                rowNumber++;
            }
        }
        wb.write(output);

        output.close();
        return file;
    }

    private void writeAdvertisingRow(Sheet sheet, Advertising advertising, int rowNumber, List<String> fieldNames, CellStyle dataCellStyle, CellStyle dateCellStyle) {
        Row row = sheet.createRow(rowNumber);
        int cellNo = 0;
        for (String fieldName : fieldNames) {
            writeAdvertisingCell(row, fieldName, advertising, cellNo, dataCellStyle, dateCellStyle);
            cellNo++;
        }
    }

    private String getCellValueForRequests(List<Integer> requestIds) {
        StringBuilder sb = new StringBuilder("");
        for (int i = 0; i < requestIds.size(); i++) {
            sb.append("Request no ").append(requestIds.get(i)).append("\n");
        }
        return sb.toString();
    }

    private void writeAdvertisingCell(Row row, String fieldName, Advertising advertising, int cellNumber, CellStyle dataCellStyle, CellStyle dateCellStyle) {
        Cell cell = row.createCell(cellNumber);
        cell.setCellStyle(dataCellStyle);
        AdvertisingExportedField field = AdvertisingExportedField.valueOf(fieldName);
        switch (field) {
            case id:
                cell.setCellValue(advertising.id + "/ADV");
                break;
            case status:
                cell.setCellValue(advertising.status.name());
                break;
            case advertisingMediaSelection:
                cell.setCellValue(advertising.advertisingMediaSelection);
                break;
            case requestIds:
                cell.setCellValue(getCellValueForRequests(advertising.requestIds));
                break;
            case assigneeFullName:
                cell.setCellValue(advertising.assigneeFullName);
                break;
            case assigneeEmail:
                cell.setCellValue(advertising.assigneeEmail);
                break;
            case followerFullName:
                cell.setCellValue(advertising.followerFullName);
                break;
            case followerEmail:
                cell.setCellValue(advertising.followerEmail);
                break;
            case creatorFullName:
                cell.setCellValue(advertising.creatorFullName);
                break;
            case creatorEmail:
                cell.setCellValue(advertising.creatorEmail);
                break;
            case description:
                cell.setCellValue(advertising.description);
                break;
            case submittedDate:
                if (advertising.submittedDate != null) {
                    cell.setCellValue(advertising.submittedDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case validatedTime:
                if (advertising.validatedTime != null) {
                    cell.setCellValue(advertising.validatedTime);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case licenseRequestingDate:
                if (advertising.licenseRequestingDate != null) {
                    cell.setCellValue(advertising.licenseRequestingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case licenseNumber:
                cell.setCellValue(advertising.licenseNumber);
                break;
            case licenseReceivingDate:
                if (advertising.licenseReceivingDate != null) {
                    cell.setCellValue(advertising.licenseReceivingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case licenseExpiredDate:
                if (advertising.licenseExpiredDate != null) {
                    cell.setCellValue(advertising.licenseExpiredDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case brand:
                if (advertising.brandId != null) {
                    cell.setCellValue(advertising.brandName);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case revisingDate:
                if (advertising.revisingDate != null) {
                    cell.setCellValue(advertising.revisingDate);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case contentFiles:
                if (advertising.contentFiles != null) {
                    StringBuilder sb = new StringBuilder();
                    advertising.contentFiles.forEach((key, value) -> {
                        sb.append(value).append(" / ");
                    });
                    cell.setCellValue(sb.toString());
                }
                break;
            case referencesFiles:
                if (advertising.referencesFiles != null) {
                    StringBuilder sb = new StringBuilder();
                    advertising.referencesFiles.forEach((key, value) -> {
                        sb.append(value).append(" / ");
                    });
                    cell.setCellValue(sb.toString());
                }
                break;
            default:
                break;
        }
    }

    private void writeCraRequestRow(Sheet sheet, CraRequest craRequest, int rowNumber, List<String> fieldNames, CellStyle dataCellStyle, CellStyle dateCellStyle) {
        Row row = sheet.createRow(rowNumber);
        for (int i = 0; i < fieldNames.size(); i++) {
            writeCraRequestCell(row, fieldNames.get(i), craRequest, i, dataCellStyle, dateCellStyle);
        }
    }

    private void writeCraRequestCell(Row row, String fieldName, CraRequest craRequest, int cellNumber, CellStyle dataCellStyle, CellStyle dateCellStyle) {
        Cell cell = row.createCell(cellNumber);
        cell.setCellStyle(dataCellStyle);
        CraRequestExportedField field = CraRequestExportedField.valueOf(fieldName);
        switch (field) {
            case id:
                cell.setCellValue(craRequest.id + "/CRA");
                break;
            case status:
                cell.setCellValue(craRequest.status.name());
                break;
            case creatorEmail:
                cell.setCellValue(craRequest.creatorEmail);
                break;
            case creatorFullName:
                cell.setCellValue(craRequest.creatorFullName);
                break;
            case assigneeId:
                if (craRequest.assigneeId != null) {
                    cell.setCellValue(craRequest.assigneeId);
                }
                break;
            case assigneeEmail:
                cell.setCellValue(craRequest.assigneeEmail);
                break;
            case assigneeFullName:
                cell.setCellValue(craRequest.assigneeFullName);
                break;
            case followerId:
                if (craRequest.followerId != null) {
                    cell.setCellValue(craRequest.followerId);
                }
                break;
            case followerEmail:
                cell.setCellValue(craRequest.followerEmail);
                break;
            case followerFullName:
                cell.setCellValue(craRequest.followerFullName);
                break;
            case groupId:
                if (craRequest.groupId != null) {
                    cell.setCellValue(craRequest.groupId);
                }
                break;
            case requestIds:
                cell.setCellValue(craRequest.requestIds==null?null:craRequest.requestIds.toString());
                break;
            case exposition:
                cell.setCellValue(craRequest.expositionLevel+"-"+craRequest.expositionDetail);
                break;
            case expositionLevel:
                cell.setCellValue(craRequest.expositionLevel);
                break;
            case expositionDetail:
                cell.setCellValue(craRequest.expositionDetail);
                break;
            case advertisementType:
                cell.setCellValue(craRequest.advertisementType);
                break;
            case timeline:
                if (craRequest.timeline != null) {
                    cell.setCellValue(craRequest.timeline);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case dateOfSubmitting:
                if (craRequest.dateOfSubmitting != null) {
                    cell.setCellValue(craRequest.dateOfSubmitting);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case dateOfRequestingProofDocument:
                if (craRequest.dateOfRequestingProofDocument != null) {
                    cell.setCellValue(craRequest.dateOfRequestingProofDocument);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case dateOfReceivingProofDocument:
                if (craRequest.dateOfReceivingProofDocument != null) {
                    cell.setCellValue(craRequest.dateOfReceivingProofDocument);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case dateOfStradRiskAssessment:
                if (craRequest.dateOfStradRiskAssessment != null) {
                    cell.setCellValue(craRequest.dateOfStradRiskAssessment);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case dateOfCompleted:
                if (craRequest.dateOfCompleted != null) {
                    cell.setCellValue(craRequest.dateOfCompleted);
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case contentFiles:
                if (craRequest.contentFiles != null) {
                    StringBuilder sb = new StringBuilder();
                    craRequest.contentFiles.forEach((key, value) -> {
                        sb.append(value).append(" / ");
                    });
                    cell.setCellValue(sb.toString());
                }
                break;
            case referenceFiles:
                if (craRequest.referencesFiles != null) {
                    StringBuilder sb = new StringBuilder();
                    craRequest.referencesFiles.forEach((key, value) -> {
                        sb.append(value).append(" / ");
                    });
                    cell.setCellValue(sb.toString());
                }
                break;
            case proofDocuments:
                if (craRequest.proofDocuments != null) {
                    StringBuilder sb = new StringBuilder();
                    craRequest.proofDocuments.forEach((key, value) -> {
                        sb.append(value).append(" / ");
                    });
                    cell.setCellValue(sb.toString());
                }
                break;
            case stradRiskAssessmentFiles:
                if (craRequest.stradRiskAssessmentFiles != null) {
                    StringBuilder sb = new StringBuilder();
                    craRequest.stradRiskAssessmentFiles.forEach((key, value) -> {
                        sb.append(value).append(" / ");
                    });
                    cell.setCellValue(sb.toString());
                }
                break;
            case messages:
                if (craRequest.messages != null) {
                    cell.setCellValue(craRequest.messages.toString());
                }
                break;
            case gmLoginHash:
                cell.setCellValue(craRequest.gmLoginHash);
                break;
            case cmLoginHash:
                cell.setCellValue(craRequest.cmLoginHash);
                break;
            case created:
                if (craRequest.created != null) {
                    cell.setCellValue(new Date(craRequest.created));
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            case updated:
                if (craRequest.updated != null) {
                    cell.setCellValue(new Date(craRequest.updated));
                    cell.setCellStyle(dateCellStyle);
                }
                break;
            default:
                break;
        }
    }
}
