org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount=5
org.quartz.scheduler.skipUpdateCheck=true
org.quartz.scheduler.instanceId=NON_CLUSTERED
quartz.scheduler.instanceName=ServerScheduler
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate
org.quartz.jobStore.dataSource=quartzDataSource
org.quartz.jobStore.tablePrefix=QRTZ_
org.quartz.jobStore.isClustered=false
org.quartz.dataSource.quartzDataSource.driver=com.mysql.cj.jdbc.Driver
org.quartz.dataSource.quartzDataSource.URL=***********************************************
org.quartz.dataSource.quartzDataSource.user=root
org.quartz.dataSource.quartzDataSource.password=haiha
org.quartz.dataSource.quartzDataSource.maxConnections=2
org.quartz.jobStore.dontSetAutoCommitFalse=false
org.quartz.scheduler.jobFactory.class=org.quartz.simpl.SimpleJobFactory
org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX
