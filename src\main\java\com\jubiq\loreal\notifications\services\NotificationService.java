package com.jubiq.loreal.notifications.services;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.advertisings.services.AdvertisingService;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.InvalidRequestException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.FileService;
import com.jubiq.loreal.common.service.HistoryService;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.common.service.MailService;
import com.jubiq.loreal.labels.models.Label;
import com.jubiq.loreal.labels.services.LabelService;
import com.jubiq.loreal.notifications.models.*;
import com.jubiq.loreal.notifications.models.enumerations.ExportedField;
import com.jubiq.loreal.notifications.models.enumerations.NotificationStatus;
import com.jubiq.loreal.notifications.models.enumerations.Presentation;
import com.jubiq.loreal.umgr.models.Group;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.GroupService;
import com.jubiq.loreal.umgr.services.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.jubiq.loreal.LorealConstants.*;
import static com.jubiq.loreal.notifications.models.enumerations.NotificationStatus.*;

/**
 * Created by vietnq on 11/29/15.
 */
@Singleton
public class NotificationService extends JubiqService<Long, Notification> {
    protected static Logger LOGGER = LoggerFactory.getLogger(NotificationService.class);
    private BrandService brandService;
    private RangeService rangeService;
    private ProductTypeService productTypeService;
    private UserService userService;
    private ExportService exportService;
    private HistoryService historyService;
    private FileService fileService;
    private AlertService alertService;
    private GroupService groupService;
    private IngredientService ingredientService;
    private NoteService noteService;
    private ObjectMapper jsonMapper;
    private ConfigurationService configurationService;
    private MailService mailService;
    @Inject
    private AdvertisingService advertisingService;

    @Inject
    private LabelService labelService;
    @Inject
    private FilterService filterService;

    @Inject
    private PresentationDetailService presentationDetailService;

    @Inject
    public NotificationService(DaoFactory daoFactory,
                               BrandService brandService,
                               RangeService rangeService,
                               ProductTypeService productTypeService,
                               UserService userService,
                               ExportService exportService,
                               HistoryService historyService,
                               FileService fileService,
                               AlertService alertService,
                               GroupService groupService,
                               IngredientService ingredientService,
                               NoteService noteService,
                               ConfigurationService configurationService,
                               MailService mailService
    ) {
        this.dao = daoFactory.getNotificationDao();
        this.brandService = brandService;
        this.rangeService = rangeService;
        this.productTypeService = productTypeService;
        this.userService = userService;
        this.exportService = exportService;
        this.historyService = historyService;
        this.fileService = fileService;
        this.alertService = alertService;
        this.groupService = groupService;
        this.ingredientService = ingredientService;
        this.noteService = noteService;
        this.configurationService = configurationService;
        this.mailService = mailService;
        jsonMapper = new ObjectMapper();
        jsonMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Override
    public Notification get(Long aLong) throws JubiqPersistenceException {
        Notification notification = this.dao.get(aLong);
        notification.presentationDetails = presentationDetailService.findByNotificationId(aLong);
        notification.advertising = advertisingService.findByNotificationId(aLong);
        fillDuplicateNotification(notification);
        //fillLabelInfo(notification);
        fillProductTypeDesc(notification);
        return notification;
    }

    private void fillProductTypeDesc(Notification notification) {
        if (notification.productTypeIds != null && notification.productTypeIds.size() > 0) {
            StringBuilder sb = new StringBuilder("");
            if (notification.productTypeIds != null && notification.productTypeIds.size() > 0) {
                for (long productTypeId : notification.productTypeIds) {
                    ProductType productType = productTypeService.get(productTypeId);
                    sb.append(productType.description).append("\n");
                }
            }
            notification.productTypeDesc = sb.toString();
        }
    }

    private void fillLabelInfo(Notification notification) {
        for (PresentationDetail presentationDetail : notification.presentationDetails) {
            List<Label> labels = labelService.findByPresentationDetailId(presentationDetail.id);
            for (Label label : labels) {
                label.presentationDetails = new ArrayList<>();
                if (label.presentationDetailIds != null)
                    for (String presentationId : label.presentationDetailIds) {
                        if (presentationDetail.id.toString().equals(presentationId)) {
                            label.presentationDetails.add(presentationDetail);
                        }
                    }
            }
//            presentationDetail.labels = labels;
        }
    }


    private void fillDuplicateNotification(Notification notification) {
        if (notification != null) {
//            notification.presentationDetails = presentationDetailService.findByNotificationId(notification.id);
//            notification.advertising = advertisingService.findByNotificationId(notification.id);

            // them cac noti bi trung them 3 tieu chi
            List<Long> duplicateNames = findDupWithProductName(notification.id, notification.productName);
            for (PresentationDetail presentationDetail : notification.presentationDetails) {
                presentationDetail.duplicateNotifications = new HashMap<Long, String>();
                presentationDetail.mapDuplicateNotification = new HashMap<String, Set<Long>>();
                Set<Long> dupNameIds = new TreeSet<>(Collections.reverseOrder());
                for (Long tmpNoti : duplicateNames) {
                    presentationDetail.duplicateNotifications.put(tmpNoti, "Duplicate Name");
                    dupNameIds.add(tmpNoti);
                }
                presentationDetail.mapDuplicateNotification.put(notification.productName, dupNameIds);
                notification.mapDuplicateNotification.put(notification.productName, dupNameIds);
                List<Long> duplicateFils = presentationDetailService.findByFillCode(notification.id, presentationDetail.filCode);

                Set<Long> dupFilIds = new TreeSet<>(Collections.reverseOrder());
                for (Long tmp : duplicateFils) {
                    if (presentationDetail.duplicateNotifications.containsKey(tmp)) {
                        if (!presentationDetail.duplicateNotifications.get(tmp).contains("FIL")) {
                            presentationDetail.duplicateNotifications.put(tmp, presentationDetail.duplicateNotifications.get(tmp) + ", FIL");
                            dupFilIds.add(tmp);
                        }
                    } else {
                        presentationDetail.duplicateNotifications.put(tmp, "Duplicate FIL");
                        dupFilIds.add(tmp);
                    }
                }
                if (!Strings.isNullOrEmpty(presentationDetail.filCode))
                    presentationDetail.mapDuplicateNotification.put(presentationDetail.filCode, dupFilIds);

                Set<Long> dupFalIds = new TreeSet<>(Collections.reverseOrder());
                List<Long> duplicateFormulas = presentationDetailService.findByFormula(notification.id, presentationDetail.formulaNumber);
                for (Long tmp : duplicateFormulas) {
                    if (presentationDetail.duplicateNotifications.containsKey(tmp)) {
                        if (!presentationDetail.duplicateNotifications.get(tmp).contains("FLA")) {
                            presentationDetail.duplicateNotifications.put(tmp, presentationDetail.duplicateNotifications.get(tmp) + ", FLA");
                            dupFalIds.add(tmp);
                        }
                    } else {
                        presentationDetail.duplicateNotifications.put(tmp, "Duplicate FLA");
                        dupFalIds.add(tmp);
                    }
                }
                if (!Strings.isNullOrEmpty(presentationDetail.formulaNumber))
                    presentationDetail.mapDuplicateNotification.put(presentationDetail.formulaNumber, dupFalIds);

                Set<Long> dupShadeIds = new TreeSet<>(Collections.reverseOrder());
                List<Long> duplicateShadeName = presentationDetailService.findByShadeName(notification.id, presentationDetail.shadeName);
                for (Long tmp : duplicateShadeName) {
                    if (presentationDetail.duplicateNotifications.containsKey(tmp)) {
                        if (!presentationDetail.duplicateNotifications.get(tmp).contains("SHADE")) {
                            presentationDetail.duplicateNotifications.put(tmp, presentationDetail.duplicateNotifications.get(tmp) + ", SHADE");
                            dupShadeIds.add(tmp);
                        }
                    } else {
                        presentationDetail.duplicateNotifications.put(tmp, "Duplicate SHADE");
                        dupShadeIds.add(tmp);
                    }
                }
                if (!Strings.isNullOrEmpty(presentationDetail.shadeName))
                    presentationDetail.mapDuplicateNotification.put(presentationDetail.shadeName, dupShadeIds);
                // sort by id giam dan
                Map<Long, String> tmpMap1 = new TreeMap<Long, String>(Collections.reverseOrder());
                tmpMap1.putAll(presentationDetail.duplicateNotifications);
                presentationDetail.duplicateNotifications.clear();
                presentationDetail.duplicateNotifications = tmpMap1;

//                // sort id giam dan
//                Map<String, Tr<Long>> tmpMap2 = new TreeMap<String, Set<Long>>(Collections.reverseOrder());
//                tmpMap2.putAll(presentationDetail.mapDuplicateNotification);
//                presentationDetail.mapDuplicateNotification.clear();
//                presentationDetail.mapDuplicateNotification= tmpMap2;
            }
        }
    }

    @Override
    public List<Notification> search(String query, int limit, int offset, String order) throws JubiqPersistenceException {
        List<Notification> notifications = this.dao.search(query, limit, offset, order);
        for (Notification notification : notifications) {
            // chuan hoa lai du lieu shadeName va commonShadeName
            List<PresentationDetail> presentationDetails = presentationDetailService.findByNotificationId(notification.id);
            for (PresentationDetail pre : presentationDetails) {
                if (notification.presentationType == Presentation.PALETTE) {
                    if (!Strings.isNullOrEmpty(pre.shadeName) && Strings.isNullOrEmpty(pre.commonShadeName)) {
                        pre.commonShadeName = pre.shadeName;
                        pre.shadeName = null;
                    }
                } else if (notification.presentationType == Presentation.SINGLE) {
                    pre.commonShadeName = pre.shadeName;
                    pre.shadeName = null;
                }
            }
            notification.presentationDetails = presentationDetails;
            notification.advertising = advertisingService.findByNotificationId(notification.id);
            notification.hasLabel = labelService.isNotificationHasLabel(notification.id);
            fillDuplicateNotification(notification);
            fillProductTypeDesc(notification);
        }
        return notifications;
    }

    public List<Notification> searchForExportXls(String query, int limit, int offset, String order) throws JubiqPersistenceException {
        List<Notification> notifications = this.dao.search(query, limit, offset, order);
        for (Notification notification : notifications) {
            notification.presentationDetails = presentationDetailService.findByNotificationId(notification.id);
            notification.advertising = advertisingService.findByNotificationId(notification.id);
            notification.hasLabel = labelService.isNotificationHasLabel(notification.id);
            fillProductTypeDesc(notification);
        }
        return notifications;
    }

    @Override
    public Notification create(Notification entity, JubiqSession session) throws JubiqPersistenceException {
        if (entity.status == null) {
            entity.status = WAIT_FOR_MKT_SUBMISSION;
        }
        entity.creatorId = entity.followerId = session.userId;
        entity.groupId = session.groupId;
        if (entity.id == null) {
            Long currentId = currentIncrementValue();
            if (currentId < NOTIFICATION_START_ID) {
                entity.id = NOTIFICATION_START_ID;
            } else {
                entity.id = currentId;
            }
        }
        beforeCreate(entity);
        Notification notification = this.dao.create(entity);
        for (PresentationDetail detail : entity.presentationDetails) {
            detail.notificationId = notification.id;
            this.presentationDetailService.create(detail, session);
        }
        if (session != null) {
            historyService.createHistory(new History(notification.id, "notification", "create", session.userId, session.fullName, null, null, null));
        }
        return notification;
    }

    @Override
    public void update(Long aLong, Notification entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        Notification existing = get(aLong);
        updateNotification(aLong, entity, session);

        Notification updated = get(aLong);

        String oldValue = null;
        String newValue = null;
        try {
            oldValue = jsonMapper.writeValueAsString(existing);
            newValue = jsonMapper.writeValueAsString(updated);

        } catch (JsonProcessingException e) {
            LOGGER.info("cannot parse json string");
        }

        historyService.createHistory(new History(aLong, "notification", "update", session.userId, session.fullName, oldValue, newValue, null));
//        if(session.groupName.equals("SCI Manager")){
        String differField = entity.compareFields(existing).stream().collect(Collectors.joining(", "));
        LOGGER.info("Different field for " + updated.id + " : " + differField);
        if (!differField.isEmpty()) {
            Alert alert = new Alert(session.userId, session.fullName, "update-notification");
            alert.params.put("differFields", differField);
            alert.params.put("notificationId", updated.id.toString());
            alertService.createAlert(alert, null, updated.followerId);
        }
//        }
    }

    @Override
    public void delete(Long aLong, JubiqSession session) throws JubiqPersistenceException {
        Notification notification = this.dao.get(aLong);
        if (notification.status == NotificationStatus.COMPLETED) {
            throw new InvalidRequestException("Invalid status, COMPLETED can't delete");
        }
        this.dao.delete(aLong);
        //delete related presentation details
        String sql = "update presentation_details set deleted=:deleted where notification_id=:notificationId";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("deleted", System.currentTimeMillis());
        map.put("notificationId", aLong);
        this.dao.executeUpdate(sql, map);

        historyService.createHistory(new History(aLong, "notification", "delete", session.userId, session.fullName, null, null, null));
        Alert alert = new Alert(session.userId, session.fullName, "delete");
        alert.params.put("notificationId", aLong.toString());
        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, null, notification.followerId);
    }

    @Override
    protected void validateEntity(Notification entity) {
        if (entity.presentationDetails == null || entity.presentationDetails.size() == 0) {
            throw new InvalidRequestException("No presentation detail");
        }
        Brand brand = brandService.get(entity.brandId);

        if (entity.followerId != null) {
            User follower = userService.getDao().get(entity.followerId);
            entity.followerEmail = follower.email;
            entity.followerFullName = follower.fullName;
        }
        if (entity.assigneeId != null) {
            User assignee = userService.getDao().get(entity.assigneeId);
            entity.assigneeEmail = assignee.email;
            entity.assigneeFullName = assignee.fullName;
        }
        if (entity.creatorId != null) {
            User creator = userService.getDao().get(entity.creatorId);
            entity.creatorEmail = creator.email;
            entity.creatorFullName = creator.fullName;
        }
        entity.brandName = brand.name;

        /*ProductType productType = productTypeService.get(entity.productTypeId);
        entity.productTypeDesc = productType.description;*/

        if (entity.productRangeId != null) {
            if (entity.notiType.equals("Medical device") && entity.productRangeId == 0) {
                entity.productRangeId = null;
            }
            else{
                Range range = rangeService.get(entity.productRangeId);
                entity.productRangeName = range.name;
            }
        }
    }

    public void submitToSci(Long id, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (!session.userId.equals(notification.followerId)) {
            throw new UnauthorizedException("You are not follower of the notification");
        }
        if (notification.status != NotificationStatus.WAIT_FOR_MKT_SUBMISSION) {
            throw new InvalidRequestException("Invalid status, must be ready to submit");
        }
        Calendar submittingCal = Calendar.getInstance();
        Date submittingDate = submittingCal.getTime();
        submittingCal.add(Calendar.DATE, 29 * 5);
        if (notification.launchTime.getTime() < submittingCal.getTime().getTime()) {
            throw new InvalidRequestException("Launch time must be at least 5 months after submitting date");
        }
        submittingCal.add(Calendar.DATE, -29);
        if (notification.shipmentRequestTime.getTime() < submittingCal.getTime().getTime()) {
            throw new InvalidRequestException("Shipment request time must be at least 4 months after submitting date");
        }


        notification.submittingDate = submittingDate;
        if (notification.notiType.equals("Medical device")) {
            notification.status = WAIT_FOR_MEDICAL_DEVICE_CLASSIFICATION;
        }
        else{
            notification.status = WAIT_FOR_SC_VALIDATION;
        }
        this.dao.update(notification);

        historyService.createHistory(new History(id, "notification", "submit", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "submit");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, null);

        Alert logisticAlert = new Alert(session.userId, session.fullName, "submit");
        logisticAlert.params.put("notificationId", id.toString());
        logisticAlert.params.put("productName", notification.productName);
        alertService.createAlert(logisticAlert, LOGISTICS_GROUP_ID, null);
    }

    public void medicalDeviceDefined(Long id, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != NotificationStatus.WAIT_FOR_MEDICAL_DEVICE_CLASSIFICATION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_MEDICAL_DEVICE_CLASSIFICATION");
        }
        if (!notification.notiType.equals("Medical device")) {
            throw new InvalidRequestException("Invalid notification type, must be Medical device");
        }

        notification.medicalDeviceDefinedDate = new Date();
        notification.status = WAIT_FOR_SC_VALIDATION;

        this.dao.update(notification);

        historyService.createHistory(new History(id, "notification", "medical-device-defined", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "medical-device-defined");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, null, notification.followerId);

        Alert logisticAlert = new Alert(session.userId, session.fullName, "medical-device-defined");
        logisticAlert.params.put("notificationId", id.toString());
        logisticAlert.params.put("productName", notification.productName);
        alertService.createAlert(logisticAlert, LOGISTICS_GROUP_ID, null);
    }


    public void recall(Long id, String reason, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(notification.followerId)) {
            throw new UnauthorizedException("You are not the follower of the notification");
        }
        if (notification.status == COMPLETED || notification.status == WAIT_FOR_MKT_SUBMISSION || notification.status == RECALLED || notification.status == WAIT_FOR_REJECTION) {
            throw new InvalidRequestException("Invalid status, must not be COMPLETED, WAIT_FOR_MKT_SUBMISSION, RECALLED or WAIT_FOR_REJECTION");
        }

        NotificationStatus newStatus = WAIT_FOR_REJECTION;
        if (notification.status == WAIT_FOR_MEDICAL_DEVICE_CLASSIFICATION || notification.status == WAIT_FOR_SC_VALIDATION || notification.status == WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN || notification.status == WAIT_FOR_SCI_VALIDATION) {
            newStatus = RECALLED;
        }
        updateStatus(id, newStatus);

        historyService.createHistory(new History(id, "notification", "recall", session.userId, session.fullName, null, null, reason));

        if (notification.validated) {
            Alert alert = new Alert(session.userId, session.fullName, "recall");
            alert.params.put("notificationId", id.toString());
            alert.params.put("productName", notification.productName);

            alertService.createAlert(alert, null, notification.assigneeId);
        }
    }

    public void revise(Long id, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(notification.followerId)) {
            throw new UnauthorizedException("You are not the follower of the notification");
        }
        if (notification.status != REJECTED && notification.status != RECALLED) {
            throw new InvalidRequestException("Invalid status, must be REJECTED or RECALLED");
        }

        if (notification.notiType.equals("Medical device")) {
            notification.status = WAIT_FOR_MEDICAL_DEVICE_CLASSIFICATION;
        }
        else{
            notification.status = WAIT_FOR_SC_VALIDATION;
        }

        Calendar revisingCal = Calendar.getInstance();
        Date revisingDate = revisingCal.getTime();

        revisingCal.add(Calendar.DATE, 29 * 5);

        if (notification.launchTime.getTime() < revisingCal.getTime().getTime()) {
            throw new InvalidRequestException("Launch time must be at least 5 months after revising date");
        }
        revisingCal.add(Calendar.DATE, -29);
        if (notification.shipmentRequestTime.getTime() < revisingCal.getTime().getTime()) {
            throw new InvalidRequestException("Shipment request time must be at least 4 months after revising date");
        }

        notification.revisingDate = revisingDate;

        this.dao.update(notification);

        historyService.createHistory(new History(id, "notification", "revise", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "revise");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        if (notification.assigneeId == null) {
            alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, null);
        } else {
            alertService.createAlert(alert, null, notification.assigneeId);
        }
    }

    public void renotify(Long id, String reason, JubiqSession session) {
        Notification entity = this.dao.get(id);
        entity.isRenoti = true;
        this.dao.update(entity);
        entity.presentationDetails = presentationDetailService.findByNotificationId(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(entity.followerId)) {
            throw new UnauthorizedException("You are not the follower of the notification");
        }
        if (entity.status != NotificationStatus.COMPLETED) {
            throw new InvalidRequestException("Invalid status, must be completed to be re-notified");
        }
        Long currentId = currentIncrementValue();
        if (currentId < NOTIFICATION_START_ID) {
            entity.id = NOTIFICATION_START_ID;
        } else {
            entity.id = currentId;
        }
        entity.created = System.currentTimeMillis();
        entity.status = WAIT_FOR_MKT_SUBMISSION;
        //entity.submittingDate = new Date();
        entity.submittingDate = null;
        entity.revisingDate = null;
        entity.rejectProductName = null;
        entity.scValidateDate = null;
        entity.medicalDeviceDefinedDate = null;
        entity.assigneeId = null;
        entity.assigneeEmail = null;
        entity.assigneeFullName = null;
        entity.validatedTime = null;
        entity.validated = entity.cfsAvailable = entity.inciAvailable = entity.davAvailable = false;
        entity.cfsRequestingDate = entity.cfsReceivingDate = entity.inciRequestingDate = entity.inciReceivingDate = null;
        entity.cfsFiles = entity.inciFiles = entity.davFiles = null;
        entity.davRequestingDate = entity.davOfficialRequestingDate = entity.davReceivingDate = entity.davExpiringDate = null;
        entity.davNotificationNumber = null;
        entity.scValidateDate = null;
        entity.isRenoti = null;
        // set lai lauch time + 6 thang theo yeu cau cua chi Yen khi renoti
        java.util.Calendar c = java.util.Calendar.getInstance();
        c.setTime(entity.launchTime);
        c.add(Calendar.MONTH, 6);
        entity.launchTime = c.getTime();
        beforeCreate(entity);
        Notification notification = this.dao.create(entity);
        for (PresentationDetail detail : entity.presentationDetails) {
            detail.id = null;
            detail.notificationId = notification.id;
            this.presentationDetailService.create(detail, session);
        }
        historyService.createHistory(new History(notification.id, "notification", "renotify", session.userId, session.fullName, id.toString(), notification.id.toString(), reason));

        Alert alert = new Alert(session.userId, session.fullName, "renotify");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alert.params.put("reason", reason);
        alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, notification.assigneeId);
    }

    public void cancel(Long id, String reason, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(notification.assigneeId) && !session.userId.equals(notification.followerId)) {
            throw new UnauthorizedException("You must be assignee or follower to cancel the request");
        }

        Group callerGroup = groupService.getDao().get(session.groupId);
        if (callerGroup.ascendantId.equals(MARKETING_GROUP_ID)) {
            if (notification.status == COMPLETED || notification.status == CANCELLED || notification.status == WAIT_FOR_CANCEL) {
                throw new InvalidRequestException("Invalid status, must not be COMPLETED or CANCELLED or WAIT_FOR_CANCEL");
            }
            if (notification.status == WAIT_FOR_MKT_SUBMISSION || notification.status == WAIT_FOR_MEDICAL_DEVICE_CLASSIFICATION || notification.status == WAIT_FOR_SC_VALIDATION || notification.status == WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN || notification.status == WAIT_FOR_SCI_VALIDATION) {
                notification.status = CANCELLED;
            } else {
                notification.status = WAIT_FOR_CANCEL;
            }
        } else if (callerGroup.ascendantId.equals(SCI_GROUP_ID)) {
            if (notification.status != WAIT_FOR_CANCEL) {
                throw new InvalidRequestException("Notification must be in WAIT_FOR_CANCEL status");
            }
            notification.status = CANCELLED;
        }

        updateStatus(id, notification.status);
        historyService.createHistory(new History(id, "notification", "cancel", session.userId, session.fullName, null, null, reason));

        Alert alert = new Alert(session.userId, session.fullName, "cancel");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alert.params.put("reason", reason);
        if (callerGroup.ascendantId.equals(MARKETING_GROUP_ID)) {
            alertService.createAlert(alert, null, notification.assigneeId);
        } else if (callerGroup.ascendantId.equals(SCI_STAFF_GROUP_ID)) {
            alertService.createAlert(alert, null, notification.followerId);
        }
    }

    public void assign(Long id, Long assigneeId, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.assigneeId == null && notification.status != WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN");
        }

        Boolean reAssign = notification.assigneeId != null;
        try {
            User user = this.userService.get(assigneeId);

            notification.assigneeId = user.id;
            notification.assigneeEmail = user.email;
            notification.assigneeFullName = user.fullName;
            if (!reAssign) {
                notification.status = WAIT_FOR_SCI_VALIDATION;
            }
        } catch (EntityNotFoundException e) {
            throw new InvalidRequestException("No assignee with id " + assigneeId);
        }
        this.dao.update(notification);

        StringBuilder sb = new StringBuilder("update labels ");
        sb.append("set updated=:updated")
                .append(",assignee_id=:assigneeId")
                .append(",assignee_email=:assigneeEmail")
                .append(",assignee_full_name=:assigneeFullName")
                .append(" where FIND_IN_SET(").append(":notificationId")
                .append(", REPLACE(REPLACE(REPLACE(notification_ids, '[', ''), ']', ''), ' ', '')) > 0 and deleted is null ");

//                .append(" where notification_ids=:notificationId and deleted is null");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("updated", System.currentTimeMillis());
        map.put("assigneeId", notification.assigneeId);
        map.put("assigneeEmail", notification.assigneeEmail);
        map.put("assigneeFullName", notification.assigneeFullName);
        map.put("notificationId", notification.id);
        this.dao.executeUpdate(sb.toString(), map);

        historyService.createHistory(new History(id, "notification", reAssign ? "re-assign" : "assign", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, reAssign ? "re-assign" : "assign");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, null, notification.assigneeId);
    }

    public void validate(Long id, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(notification.assigneeId)) {
            throw new UnauthorizedException("You are not assigned to process this notification");
        }
        if (notification.status != WAIT_FOR_SCI_VALIDATION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_SCI_VALIDATION");
        }
        notification.validated = true;
        notification.validatedTime = Calendar.getInstance().getTime();
        if (!notification.cfsAvailable && !notification.inciAvailable) {
            notification.status = WAIT_FOR_CFS_INCI;
        }
        if (notification.cfsAvailable && !notification.inciAvailable) {
            notification.status = WAIT_FOR_INCI;
        }
        if (notification.inciAvailable && !notification.cfsAvailable) {
            notification.status = WAIT_FOR_CFS;
        }
        if (notification.cfsAvailable && notification.inciAvailable) {
            notification.status = WAIT_FOR_SUBMISSION;
        }
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "validate", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "validate");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, null, notification.followerId);
    }

    public void reject(Long id, String reason, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(notification.assigneeId)) {
            throw new UnauthorizedException("You are not assigned to process this notification");
        }
        if (notification.status == COMPLETED || notification.status == REJECTED) {
            throw new InvalidRequestException("Invalid status, must not be COMPLETED or REJECTED");
        }
        notification.medicalDeviceDefinedDate = null;
        notification.validatedTime = null;
        notification.validated = false;
        notification.rejectProductName = true;
        notification.scValidateDate = null;
        notification.status = REJECTED;
        // bo sung xoa thong in ve CFS/INCI
        notification.cfsRequestingDate = null;
        notification.cfsReceivingDate = null;
        notification.inciRequestingDate = null;
        notification.inciReceivingDate = null;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "reject", session.userId, session.fullName, null, null, reason));

        Alert alert = new Alert(session.userId, session.fullName, "reject");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alert.params.put("reason", reason);
        alertService.createAlert(alert, null, notification.followerId);
    }


    public void validateProductName(Long id, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_SC_VALIDATION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_SC_VALIDATION");
        }
        if (notification.assigneeId == null) {
            notification.status = WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN;
        } else {
            notification.status = WAIT_FOR_SCI_VALIDATION;
        }
        notification.rejectProductName = false;
        notification.scValidateDate = new Date();
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "validate-product-name", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "validate-product-name");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);

        if (notification.status == WAIT_FOR_SCI_DIRECTOR_TO_ASSIGN) {
            alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, notification.followerId);
        } else {
            alertService.createAlert(alert, null, notification.followerId);

            Alert assigneeAlert = new Alert(session.userId, session.fullName, "validate-product-name");
            assigneeAlert.params.put("notificationId", id.toString());
            assigneeAlert.params.put("productName", notification.productName);
            alertService.createAlert(assigneeAlert, null, notification.assigneeId);
        }
    }


    public void rejectProductName(Long id, String reason, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_SC_VALIDATION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_SC_VALIDATION");
        }
        notification.rejectProductName = true;
        notification.status = SC_REJECTED;
        notification.scValidateDate = null;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "reject-product-name", session.userId, session.fullName, null, null, reason));

        Alert alert = new Alert(session.userId, session.fullName, "reject-product-name");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alert.params.put("reason", reason);
        alertService.createAlert(alert, null, notification.followerId);
    }


    public void updateProductName(Long id, JubiqSession session) {
        Notification notification = this.dao.get(id);

        if (!session.permissions.contains(SUPER_PERMISSION) && !session.userId.equals(notification.followerId)) {
            throw new UnauthorizedException("You are not the follower of the notification");
        }
        if (notification.status != SC_REJECTED) {
            throw new InvalidRequestException("Invalid status, must be SC_REJECTED");
        }
        notification.status = WAIT_FOR_SC_VALIDATION;
        this.dao.update(notification);

        historyService.createHistory(new History(id, "notification", "update-product-name", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "update-product-name");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        if (notification.assigneeId == null) {
            alertService.createAlert(alert, LOGISTICS_GROUP_ID, null);
        } else {
            alertService.createAlert(alert, LOGISTICS_GROUP_ID, notification.assigneeId);
        }
    }

    public void requestCfs(Long id, Date date, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_CFS && notification.status != WAIT_FOR_CFS_INCI) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_CFS or WAIT_FOR_CFS_INCI");
        }
        notification.cfsRequestingDate = date;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "request-cfs", session.userId, session.fullName, null, null, null));
    }

    public void resetRequestCfs(Long id, Date date, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_CFS && notification.status != WAIT_FOR_CFS_INCI) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_CFS or WAIT_FOR_CFS_INCI");
        }
        notification.cfsRequestingDate = null;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "reset-request-cfs", session.userId, session.fullName, null, null, null));
    }

    public void requestInci(Long id, Date date, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_INCI && notification.status != WAIT_FOR_CFS_INCI) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_INCI or WAIT_FOR_CFS_INCI");
        }
        notification.inciRequestingDate = date;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "request-inci", session.userId, session.fullName, null, null, null));
    }

    public void resetRequestInci(Long id, Date date, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_INCI && notification.status != WAIT_FOR_CFS_INCI) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_INCI or WAIT_FOR_CFS_INCI");
        }
        notification.inciRequestingDate = null;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "reset-request-inci", session.userId, session.fullName, null, null, null));
    }

    public void requestDav(Long id, Date date, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_SUBMISSION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_SUBMISSION");
        }
        notification.status = WAIT_FOR_NOTIFICATION_NUMBER;
        notification.davRequestingDate = date;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "request-dav", session.userId, session.fullName, null, null, null));
    }

    public void receiveCfs(Long id, Date date, List<String> fileIds, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_CFS && notification.status != WAIT_FOR_CFS_INCI) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_CFS or WAIT_FOR_CFS_INCI");
        }
        notification.cfsAvailable = true;
        if (notification.status == WAIT_FOR_CFS_INCI) {
            notification.status = WAIT_FOR_INCI;
        }
        if (notification.status == WAIT_FOR_CFS) {
            notification.status = WAIT_FOR_SUBMISSION;
        }
        Map<String, String> cfsFiles = new HashMap<String, String>();
        for (String fileId : fileIds) {
            cfsFiles.put(fileId, fileService.get(Long.valueOf(fileId)).name);
        }
        notification.cfsFiles = cfsFiles;
        notification.cfsReceivingDate = date;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "receive-cfs", session.userId, session.fullName, null, null, null));
    }


    public void resetReceiveCfs(Long id, Date date, List<String> fileIds, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_INCI && notification.status != WAIT_FOR_SUBMISSION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_INCI or WAIT_FOR_SUBMISSION");
        }
        if (notification.status == WAIT_FOR_SUBMISSION) {
            notification.status = WAIT_FOR_CFS;
        }
        if (notification.status == WAIT_FOR_INCI) {
            notification.status = WAIT_FOR_CFS_INCI;
        }
        notification.cfsAvailable = null;
        notification.cfsFiles = null;
        notification.cfsReceivingDate = null;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "reset-receive-cfs", session.userId, session.fullName, null, null, null));
    }

    public void receiveInci(Long id, Date date, List<String> fileIds, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_INCI && notification.status != WAIT_FOR_CFS_INCI) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_INCI or WAIT_FOR_CFS_INCI");
        }
        notification.inciAvailable = true;
        if (notification.status == WAIT_FOR_CFS_INCI) {
            notification.status = WAIT_FOR_CFS;
        }
        if (notification.status == WAIT_FOR_INCI) {
            notification.status = WAIT_FOR_SUBMISSION;
        }
        Map<String, String> inciFiles = new HashMap<String, String>();
        for (String fileId : fileIds) {
            inciFiles.put(fileId, fileService.get(Long.valueOf(fileId)).name);
        }
        notification.inciFiles = inciFiles;
        notification.inciReceivingDate = date;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "receive-inci", session.userId, session.fullName, null, null, null));
    }

    public void resetReceiveInci(Long id, Date date, List<String> fileIds, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_CFS && notification.status != WAIT_FOR_SUBMISSION) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_CFS or WAIT_FOR_SUBMISSION");
        }
        if (notification.status == WAIT_FOR_SUBMISSION) {
            notification.status = WAIT_FOR_INCI;
        }
        if (notification.status == WAIT_FOR_CFS) {
            notification.status = WAIT_FOR_CFS_INCI;
        }
        notification.inciAvailable = null;
        notification.inciFiles = null;
        notification.inciReceivingDate = null;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "reset-receive-inci", session.userId, session.fullName, null, null, null));
    }

    public void receiveDav(Long id, Date date, Date davExpiringDate, Date davOfficialRequestingDate, String davNotificationNumber, List<String> fileIds, JubiqSession session) {
        Notification notification = this.dao.get(id);
        if (notification.status != WAIT_FOR_NOTIFICATION_NUMBER) {
            throw new InvalidRequestException("Invalid status, must be WAIT_FOR_NOTIFICATION_NUMBER");
        }
        notification.davAvailable = true;
        notification.status = COMPLETED;
        notification.davExpiringDate = davExpiringDate;
        notification.davNotificationNumber = davNotificationNumber;
        notification.davOfficialRequestingDate = davOfficialRequestingDate;
        Map<String, String> davFiles = new HashMap<String, String>();
        for (String fileId : fileIds) {
            davFiles.put(fileId, fileService.get(Long.valueOf(fileId)).name);
        }
        notification.davFiles = davFiles;
        notification.davReceivingDate = date;
        this.dao.update(notification);
        historyService.createHistory(new History(id, "notification", "receive-dav", session.userId, session.fullName, null, null, null));

        Alert alert = new Alert(session.userId, session.fullName, "receive-dav");
        alert.params.put("notificationId", id.toString());
        alert.params.put("productName", notification.productName);
        alertService.createAlert(alert, LOGISTICS_GROUP_ID, notification.followerId);
    }

    public void updateStatus(Long id, NotificationStatus newStatus) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("status", newStatus);
        this.dao.updateFields(id, map);
    }

    public void transferFollower(Long oldFollowerId, Long newFollowerId, Long callerId) {
        User user = this.userService.getDao().get(newFollowerId);
        StringBuilder sb = new StringBuilder("update notifications ");
        sb.append("set updated=:updated")
                .append(",follower_id=:newFollowerId")
                .append(",follower_email=:newFollowerEmail")
                .append(",follower_full_name=:newFollowerFullName")
                .append(" where follower_id=:oldFollowerId and deleted is null");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("updated", System.currentTimeMillis());
        map.put("newFollowerId", newFollowerId);
        map.put("newFollowerEmail", user.email);
        map.put("newFollowerFullName", user.fullName);
        map.put("oldFollowerId", oldFollowerId);
        this.dao.executeUpdate(sb.toString(), map);

        StringBuilder labelSb = new StringBuilder("update labels ");
        labelSb.append("set updated=:updated")
                .append(",follower_id=:newFollowerId")
                .append(",follower_email=:newFollowerEmail")
                .append(",follower_full_name=:newFollowerFullName")
                .append(" where follower_id=:oldFollowerId and deleted is null");
        Map<String, Object> labelMap = new HashMap<String, Object>();
        labelMap.put("updated", System.currentTimeMillis());
        labelMap.put("newFollowerId", newFollowerId);
        labelMap.put("newFollowerEmail", user.email);
        labelMap.put("newFollowerFullName", user.fullName);
        labelMap.put("oldFollowerId", oldFollowerId);
        this.dao.executeUpdate(labelSb.toString(), labelMap);
    }

    public void transferFollower(JubiqSession session, String selectedItems, String query, Boolean changeAll, Boolean changeSelected, Long newFollowerId, String filterId) {
        User user = this.userService.getDao().get(newFollowerId);
        // uu tien theo thu tu filter -> query search -> notification selected
        String q = "";
        if (!Strings.isNullOrEmpty(filterId)) {
            // filter
            Filter filter = filterService.get(filterId);
            if (filter == null) {
                throw new EntityNotFoundException("No filter with id " + filterId);
            }
            q += filter.query;
        }
        if (changeAll) {
            // query search
            if (Strings.isNullOrEmpty(q))
                q = query;
            else if (!Strings.isNullOrEmpty(query))
                q += " and " + query;
        } else if (changeSelected) {
            // query search
            String qSelect = "id in (" + selectedItems.replace("\"", "") + ")";
            if (Strings.isNullOrEmpty(q))
                q = qSelect;
            else
                q += " and " + qSelect;

        }

        if (!q.equals("")) {
            q = " and " + q;
        }

        StringBuilder sb = new StringBuilder("update notifications ");
        sb.append("set updated=:updated")
                .append(",follower_id=:newFollowerId")
                .append(",follower_email=:newFollowerEmail")
                .append(",follower_full_name=:newFollowerFullName")
                .append(" where deleted is null ")
                .append(q);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("updated", System.currentTimeMillis());
        map.put("newFollowerId", newFollowerId);
        map.put("newFollowerEmail", user.email);
        map.put("newFollowerFullName", user.fullName);
        this.dao.executeUpdate(sb.toString(), map);

        String querySearch = "select * from notifications where deleted is null " + q;
        List<Notification> notifications = this.dao.search(querySearch, 10000);
        if (session != null) {
            for (Notification notification : notifications) {
                String action = "change-follower";
                String reason = "Change Follower to " + user.email;
                LOGGER.info("Create History for notification " + notification.id);
                historyService.createHistory(new History(notification.id, "notification", action, session.userId, session.fullName, null, null, reason));
                // cap nhat lai follower cua danh sach cac label dinh voi noti do
                List<Long> labels = labelService.findIdsByNotificationId(notification.id);
                for (Long labelId : labels) {
                    try {
                        LOGGER.info("Change follower for label " + labelId);
                        labelService.updateFollowerLabel(labelId.toString(), newFollowerId, user.fullName, user.email);
                        LOGGER.info("Create History for label " + labelId);
                        historyService.createHistory(new History(labelId, "label", action, session.userId, session.fullName, null, null, reason));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
//            String fromIds = "select id from notifications where deleted is null " + q;
        }
    }

    public void transferAssignee(Long oldAssigneeId, Long newAssigneeId, Long callerId) {
        User user = this.userService.getDao().get(newAssigneeId);
        StringBuilder sb = new StringBuilder("update notifications ");
        sb.append("set updated=:updated")
                .append(",assignee_id=:newAssigneeId")
                .append(",assignee_email=:newAssigneeEmail")
                .append(",assignee_full_name=:newAssigneeFullName")
                .append(" where assignee_id=:oldAssigneeId and deleted is null");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("updated", System.currentTimeMillis());
        map.put("newAssigneeId", newAssigneeId);
        map.put("newAssigneeEmail", user.email);
        map.put("newAssigneeFullName", user.fullName);
        map.put("oldAssigneeId", oldAssigneeId);
        this.dao.executeUpdate(sb.toString(), map);

        StringBuilder labelSb = new StringBuilder("update labels ");
        labelSb.append("set updated=:updated")
                .append(",assignee_id=:newAssigneeId")
                .append(",assignee_email=:newAssigneeEmail")
                .append(",assignee_full_name=:newAssigneeFullName")
                .append(" where assignee_id=:oldAssigneeId and deleted is null");
        Map<String, Object> labelMap = new HashMap<String, Object>();
        labelMap.put("updated", System.currentTimeMillis());
        labelMap.put("newAssigneeId", newAssigneeId);
        labelMap.put("newAssigneeEmail", user.email);
        labelMap.put("newAssigneeFullName", user.fullName);
        labelMap.put("oldAssigneeId", oldAssigneeId);
        this.dao.executeUpdate(labelSb.toString(), labelMap);
    }

    public File exportXls(String sheetName, List<Notification> notifications, List<String> fieldNames, Long callerId) throws IOException {

        File file = exportService.exportNotitificationsToXls(sheetName, notifications, fieldNames);
        return file;
    }

    public File exportToDavForm(Long id, Long callerId) throws IOException {
        Notification notification = get(id);
        File file = exportService.exportToDavTemplate(notification);
        return file;
    }

    public Notification duplicate(Long id, JubiqSession session) {
        Notification duplicated = get(id);
        if (!session.userId.equals(duplicated.assigneeId)) {
            throw new UnauthorizedException("Only assignee can duplicate this notification");
        }
        duplicated.id = currentIncrementValue();
        for (PresentationDetail detail : duplicated.presentationDetails) {
            detail.id = null; // create new detail when duplicating notification
        }
        return create(duplicated, session);
    }

    private Long currentIncrementValue() {
        String sql = "select auto_increment from information_schema.TABLES where TABLE_SCHEMA='loreal' and TABLE_NAME='notifications'";
        return this.dao.selectLong(sql);
    }


    public Notification add(Notification entity, JubiqSession session) {
        beforeCreate(entity);
        Notification notification = this.dao.create(entity);
        for (PresentationDetail detail : entity.presentationDetails) {
            detail.notificationId = notification.id;
            this.presentationDetailService.create(detail, session);
        }
        return notification;
    }


    public Notification split(Long id, JubiqSession session) {
        Notification entity = get(id);
        Notification splitItem = entity;
        splitItem.id = null;
        splitItem.presentationType = Presentation.SINGLE;
        List<PresentationDetail> presentationDetails = entity.presentationDetails;
        if (presentationDetails == null || presentationDetails.size() == 0) {
            return null;
        }

        PresentationDetail splitPresentationDetail = presentationDetails.get(0);
        splitPresentationDetail.id = null;
        List<PresentationDetail> splitPresentationDetails = new ArrayList<PresentationDetail>();
        splitPresentationDetails.add(splitPresentationDetail);
        splitItem.presentationDetails = splitPresentationDetails;

        Long currentId = currentIncrementValue();
        if (currentId < NOTIFICATION_START_ID) {
            splitItem.id = NOTIFICATION_START_ID;
        } else {
            splitItem.id = currentId;
        }
        beforeCreate(splitItem);
        Notification notification = this.dao.create(splitItem);
        for (PresentationDetail detail : notification.presentationDetails) {
            detail.notificationId = notification.id;
            this.presentationDetailService.create(detail, session);
        }

        Notification orgNotification = get(id);
        presentationDetails.remove(0);
        orgNotification.presentationDetails = presentationDetails;
        updateNotification(id, orgNotification, session);

        String oldReason = "Split to request no " + notification.id;
        historyService.createHistory(new History(id, "notification", "split", session.userId, session.fullName, null, null, oldReason));

        String reason = "Split from request no " + id;
        historyService.createHistory(new History(notification.id, "notification", "split", session.userId, session.fullName, null, null, reason));

        return notification;
    }

    public Notification splitSku(Long id, String skus, JubiqSession session) {
        Notification entity = get(id);
        Notification splitItem = entity;
        splitItem.id = null;
        List<PresentationDetail> presentationDetails = entity.presentationDetails;
        if (presentationDetails == null || presentationDetails.size() == 0) {
            return null;
        }
        List<PresentationDetail> splitPresentationDetails = new ArrayList<PresentationDetail>();
        for (String sku : skus.split(",")) {
            PresentationDetail splitPresentationDetail = presentationDetails.get(Integer.parseInt(sku));
            splitPresentationDetail.id = null;
            splitPresentationDetails.add(splitPresentationDetail);
        }
        splitItem.presentationDetails = splitPresentationDetails;

        if (splitPresentationDetails.size() == 1) {
            splitItem.presentationType = Presentation.SINGLE;
        }

        Long currentId = currentIncrementValue();
        if (currentId < NOTIFICATION_START_ID) {
            splitItem.id = NOTIFICATION_START_ID;
        } else {
            splitItem.id = currentId;
        }
        beforeCreate(splitItem);
        Notification notification = this.dao.create(splitItem);
        for (PresentationDetail detail : notification.presentationDetails) {
            detail.notificationId = notification.id;
            this.presentationDetailService.create(detail, session);
        }

        Notification orgNotification = get(id);
        String[] tmpSkus = skus.split(",");
        for (int i = tmpSkus.length - 1; i > 0; i--) {
            presentationDetails.remove(Integer.parseInt(tmpSkus[i]));
        }
        orgNotification.presentationDetails = presentationDetails;
        updateNotification(id, orgNotification, session);

        String oldReason = "Split to request no " + notification.id;
        historyService.createHistory(new History(id, "notification", "split", session.userId, session.fullName, null, null, oldReason));

        String reason = "Split from request no " + id;
        historyService.createHistory(new History(notification.id, "notification", "split", session.userId, session.fullName, null, null, reason));

        return notification;
    }

    public int splitAll(Long id, JubiqSession session) {
        Notification entity = get(id);
        List<PresentationDetail> presentationDetails = entity.presentationDetails;
        if (presentationDetails == null && presentationDetails.size() == 0) {
            return 0;
        }

        List<Long> splitIds = new ArrayList<Long>();
        for (int i = 0; i < presentationDetails.size(); i++) {
            if (i != 0) {
                PresentationDetail splitPresentationDetail = presentationDetails.get(i);
                Notification splitItem = entity;
                splitItem.id = null;
                splitItem.presentationType = Presentation.SINGLE;
                splitPresentationDetail.id = null;
                List<PresentationDetail> splitPresentationDetails = new ArrayList<PresentationDetail>();
                splitPresentationDetails.add(splitPresentationDetail);
                splitItem.presentationDetails = splitPresentationDetails;
                Long currentId = currentIncrementValue();
                if (currentId < NOTIFICATION_START_ID) {
                    splitItem.id = NOTIFICATION_START_ID;
                } else {
                    splitItem.id = currentId;
                }
                beforeCreate(splitItem);

                Notification notification = this.dao.create(splitItem);
                for (PresentationDetail detail : notification.presentationDetails) {
                    detail.notificationId = notification.id;
                    this.presentationDetailService.create(detail, session);
                }
                splitIds.add(notification.id);

                String reason = "Split from request no " + id;
                historyService.createHistory(new History(notification.id, "notification", "split-all", session.userId, session.fullName, null, null, reason));
            }
        }

        Notification orgNotification = get(id);
        List<PresentationDetail> orgPresentationDetails = new ArrayList<PresentationDetail>();
        orgPresentationDetails.add(presentationDetails.get(0));
        orgNotification.presentationDetails = orgPresentationDetails;
        updateNotification(id, orgNotification, session);

        String oldReason = "Split to request no " + implode(", ", splitIds);
        historyService.createHistory(new History(id, "notification", "split-all", session.userId, session.fullName, null, null, oldReason));
        return presentationDetails.size();
    }

    private void updateNotification(Long id, Notification notification, JubiqSession session) {
        beforeUpdate(id, notification);
        List<PresentationDetail> currentDetails = this.presentationDetailService.findByNotificationId(notification.id);
        List<Long> currentDetailsIds = new ArrayList<Long>();
        for (PresentationDetail presentationDetail : currentDetails) {
            currentDetailsIds.add(presentationDetail.id);
        }
        if (notification.presentationDetails != null) {
            for (PresentationDetail detail : notification.presentationDetails) {
                if (detail.id == null) {
                    detail.notificationId = notification.id;
                    this.presentationDetailService.create(detail, session);
                } else {
                    this.presentationDetailService.update(detail.id, detail, session);
                    if (currentDetailsIds.contains(detail.id)) {
                        currentDetailsIds.remove(detail.id);
                    }
                }
            }
        }
        for (Long detailsId : currentDetailsIds) {
            this.presentationDetailService.delete(detailsId, session);
        }
        this.dao.update(notification);
    }

    public Map<String, List<Ingredient>> getNotificationIngredients(Long id) {
        Notification notification = get(id);
        Map<String, List<Ingredient>> map = new HashMap<String, List<Ingredient>>();
        for (PresentationDetail detail : notification.presentationDetails) {
            String key = detail.formulaNumber + "&" + detail.filCode;
            map.put(key, ingredientService.findByFLAandFIL(detail.formulaNumber, detail.filCode));
        }
        return map;
    }

    public int countNotJoin(String whereExp) throws JubiqPersistenceException {
        StringBuilder sb = new StringBuilder("select count(DISTINCT notifications.id) from ")
                .append("notifications right join presentation_details")
                .append(" on notifications.id=presentation_details.notification_id")
                .append(" where notifications.deleted is NULL and presentation_details.deleted is NULL");
        if (whereExp != null && whereExp.length() > 0) {
            sb.append(" AND ").append("(").append(whereExp).append(")");
        }

        return this.dao.selectInt(sb.toString());
    }

    public String implode(String separator, List<Long> data) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < data.size() - 1; i++) {
            sb.append(data.get(i));
            sb.append(separator);
        }
        sb.append(data.get((data.size() - 1)));
        return sb.toString();
    }

    public static int days[] = {180};

    public void executeNotificationExpireJob() {
        List<User> sciUsers = userService.usersByGroup(SCI_MANAGER_GROUP_ID);
        for (User user : sciUsers) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            for (int day : days) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, day);
                String afterDay = dateFormat.format(calendar.getTime());
                String toDay = dateFormat.format(Calendar.getInstance().getTime());
                List<Notification> notifications = reportExpiredNotification(toDay, afterDay);
                if (notifications.size() > 0) {
                    String sheetName = "Expired Notifications";
                    List<String> fieldNames = new ArrayList<String>();
                    for (ExportedField exportedField : ExportedField.values()) {
                        fieldNames.add(exportedField.name());
                    }

                    try {
                        File file = exportService.exportNotitificationsToXls(sheetName, notifications, fieldNames);
                        String body = configurationService.getAlertEmail("expired");
                        mailService.sendMail(user.email, "Notification Tools", body, file.getAbsolutePath(), null);
                    } catch (IOException e) {
                        System.out.println("Caught inside export notification to xls.");
                    }
                }
            }
        }

        List<String> groupNames = new ArrayList<String>();
        groupNames.add("Marketing");
        groupNames.add("CPD");
        groupNames.add("ACD");
        groupNames.add("PPD");
        groupNames.add("LUXE");

        List<User> marketingUsers = userService.usersOfGroups(groupNames);
        for (User user : marketingUsers) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            for (int day : days) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, day);
                String afterDay = dateFormat.format(calendar.getTime());
                List<Notification> notifications = reportExpiredNotificationForFollower(afterDay, user.id);
                if (notifications.size() > 0) {
                    String sheetName = "Expired Notifications";
                    List<String> fieldNames = new ArrayList<String>();
                    for (ExportedField exportedField : ExportedField.values()) {
                        fieldNames.add(exportedField.name());
                    }

                    try {
                        File file = exportService.exportNotitificationsToXls(sheetName, notifications, fieldNames);
                        String body = configurationService.getAlertEmail("expired");
                        mailService.sendMail(user.email, "Notification Tools", body, file.getAbsolutePath(), null);
                    } catch (IOException e) {
                        System.out.println("Caught inside export notification to xls.");
                    }
                }
            }
        }
    }

    public static int cfsDays[] = {60};
    public static int inciDays[] = {30};

    public void excuteCfsInciExpireJob() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (int day : cfsDays) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -day);
            String afterDay = dateFormat.format(calendar.getTime());
            List<Notification> notifications = reportExpiredCfs(afterDay);
            alertExpiredCfs(notifications);
        }

        for (int day : inciDays) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -day);
            String afterDay = dateFormat.format(calendar.getTime());
            List<Notification> notifications = reportExpiredInci(afterDay);
            alertExpiredInci(notifications);
        }
    }

    private void alertExpiredCfs(List<Notification> notifications) {
        for (Notification notification : notifications) {
            Alert alert = new Alert(notification.followerId, notification.followerFullName, "expiredCfs");
            alert.params.put("notificationId", notification.id.toString());
            alert.params.put("productName", notification.productName);
            alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, null);
        }
    }


    private void alertExpiredInci(List<Notification> notifications) {
        for (Notification notification : notifications) {
            Alert alert = new Alert(notification.followerId, notification.followerFullName, "expiredInci");
            alert.params.put("notificationId", notification.id.toString());
            alert.params.put("productName", notification.productName);
            alertService.createAlert(alert, SCI_MANAGER_GROUP_ID, null);
        }
    }

    public List<Notification> reportExpiredNotification(String toDay, String expiredDay) {
        return this.dao.search("status = 'COMPLETED' AND dav_expiring_date >= '" + toDay + "' AND dav_expiring_date <= '" + expiredDay + "'", 100, 0, null);
    }

    public List<Notification> reportExpiredNotificationForFollower(String expiredDay, Long follwerId) {
        return this.dao.search("status = 'COMPLETED' AND follower_id = " + follwerId + " AND dav_expiring_date <= '" + expiredDay + "'", 100, 0, null);
    }

    public List<Notification> reportExpiredCfs(String expiredDay) {
        return this.dao.search("(cfs_available is NULL OR cfs_available = 0) AND cfs_requesting_date = '" + expiredDay + "'", 100, 0, null);
    }

    public List<Notification> reportExpiredInci(String expiredDay) {
        return this.dao.search("(inci_available is NULL OR inci_available = 0) AND inci_requesting_date = '" + expiredDay + "'", 100, 0, null);
    }

    public List<Long> findDupWithProductName(Long id, String englishProductName) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String toDay = dateFormat.format(Calendar.getInstance().getTime());
        /*String sql = "select distinct(notifications.id) from notifications inner join presentation_details on notifications.id = presentation_details.notification_id where notifications.product_name=:product_name and notifications.id <> " + id + " and ( notifications.dav_expiring_date >= '" + toDay + "' or " + " notifications.dav_expiring_date is null )";*/
        String sql = "select distinct(notifications.id) from notifications inner join presentation_details on notifications.id = presentation_details.notification_id where notifications.product_name=:product_name and notifications.id <> " + id;
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("product_name", englishProductName);
        return this.dao.selectLongID(sql, map);
    }
}
