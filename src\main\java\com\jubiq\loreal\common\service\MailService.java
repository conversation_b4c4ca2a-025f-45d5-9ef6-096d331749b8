package com.jubiq.loreal.common.service;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;

import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.Session;
import java.util.Properties;

/**
 * Created by vietnq on 12/25/15.
 */
@Singleton
public class MailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MailService.class);
    private final AsyncService asyncService;
    private Session session;
    private String from;

    @Inject
    public MailService(final AsyncService asyncService, final MailConfiguration configuration) {
        try {
            Properties props = new Properties();
            props.put("mail.smtp.host", configuration.host);
            props.put("mail.smtp.port", configuration.port);
            props.put("mail.smtp.auth", configuration.auth);
            props.put("mail.smtp.starttls.enable", configuration.tls);
            props.put("mail.smtp.ssl.enable", configuration.ssl);
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
            if(configuration.from != null && configuration.from.length() > 0) {
                from = configuration.from;
            }
            session = Session.getDefaultInstance(props,
                    new javax.mail.Authenticator() {
                        protected PasswordAuthentication getPasswordAuthentication() {
                            return new PasswordAuthentication(configuration.username,configuration.password);
                        }
                    });
        } catch (Exception ex) {
            LOGGER.error("Could not find mail session. {}", ex.getMessage(), ex);
        }

        this.asyncService = asyncService;
    }

    public void sendMail(String recipient, String subject, String body, String fileName, DataSource dataSource) {
        LOGGER.debug("Send email to: {}, subject: {}", recipient, subject);
        if (recipient == null || subject == null || body == null) {
            throw new IllegalArgumentException();
        }

        if (dataSource != null) {
            asyncService.execute(new SendMailTask(from, recipient, subject, body, dataSource));
        } else {
            asyncService.execute(new SendMailTask(from, recipient, subject, body, fileName));
        }
    }

    public void sendMail(String recipient, String subject, String body, String ...fileNames) {
        LOGGER.debug("Send email to: {}, subject: {}", recipient, subject);
        if (recipient == null || subject == null || body == null) {
            throw new IllegalArgumentException();
        }

        asyncService.execute(new SendMailTask(from, recipient, subject, body, fileNames));
    }

    private class SendMailTask implements Runnable {

        private String from;
        private String recipient;
        private String subject;
        private String body;
        private String fileName;
        private DataSource dataSource;

        private String[] fileNames;

        public SendMailTask(String from, String recipient, String subject, String body, String fileName) {
            this.from = from;
            this.recipient = recipient;
            this.subject = subject;
            this.body = body;
            this.fileName = fileName;
        }

        public SendMailTask(String from, String recipient, String subject, String body, DataSource source) {
            this.from = from;
            this.recipient = recipient;
            this.subject = subject;
            this.body = body;
            this.dataSource = source;
            this.fileName = source.getName();
        }

        public SendMailTask(String from, String recipient, String subject, String body, String[] fileNames) {
            this.from = from;
            this.recipient = recipient;
            this.subject = subject;
            this.body = body;
            this.fileNames = fileNames;
        }

        @Override
        public void run() {
            try {
                if((fileNames == null || fileNames.length == 0) && (fileName == null || fileName.length() == 0) && dataSource == null) {
                    MimeMessage msg = new MimeMessage(session);
                    msg.setContent(body, "text/html; charset=utf-8");
                    msg.setSubject(subject);
                    msg.setFrom(new InternetAddress(from,"Loreal Vietnam"));
                    msg.addRecipients(Message.RecipientType.TO, InternetAddress.parse(recipient));
                    Transport.send(msg);
                } else if(dataSource != null || fileName != null){
                    MimeMessage msg = new MimeMessage(session);
                    msg.setSubject(subject);
                    msg.setFrom(new InternetAddress(from));
                    msg.addRecipients(Message.RecipientType.TO, InternetAddress.parse(recipient));

                    BodyPart messageBodyPart = new MimeBodyPart();
                    messageBodyPart.setContent(body, "text/html; charset=utf-8");
                    Multipart multipart = new MimeMultipart();
                    multipart.addBodyPart(messageBodyPart);

                    // Attachment if any
                    messageBodyPart = new MimeBodyPart();
                    if (dataSource != null) {
                        messageBodyPart.setDataHandler(new DataHandler(dataSource));
                        messageBodyPart.setFileName(dataSource.getName());
                    } else {
                        DataSource source = new FileDataSource(fileName);
                        messageBodyPart.setDataHandler(new DataHandler(source));
                        messageBodyPart.setFileName(source.getName());
                    }
                    multipart.addBodyPart(messageBodyPart);
                    msg.setContent(multipart, "text/html; charset=utf-8");
                    Transport.send(msg);
                }else if(fileNames.length > 0){
                    MimeMessage msg = new MimeMessage(session);
                    msg.setSubject(subject);
                    msg.setFrom(new InternetAddress(from));
                    msg.addRecipients(Message.RecipientType.TO, InternetAddress.parse(recipient));

                    BodyPart messageBodyPart = new MimeBodyPart();
                    messageBodyPart.setContent(body, "text/html; charset=utf-8");
                    Multipart multipart = new MimeMultipart();
                    multipart.addBodyPart(messageBodyPart);

                    // Attachment if any
                    for(String fileName : fileNames){
                        messageBodyPart = new MimeBodyPart();
                        DataSource source = new FileDataSource(fileName);
                        messageBodyPart.setDataHandler(new DataHandler(source));
                        messageBodyPart.setFileName(source.getName());
                        multipart.addBodyPart(messageBodyPart);
                    }
                    msg.setContent(multipart, "text/html; charset=utf-8");
                    Transport.send(msg);
                }
            } catch (Exception ex) {
                LOGGER.error("Failed to send email to {}.", recipient, ex);
            }
        }
    }
}
