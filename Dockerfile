# S? d?ng image Java chính th?c
FROM openjdk:8-jdk


RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        fontconfig \
        fonts-dejavu-core \
        fonts-dejavu-extra \
        libfreetype6 \
        libxrender1 \
        libxtst6 \
        libxi6 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app/deploy

COPY target/loreal-1.0-SNAPSHOT.jar /app/deploy/
COPY loreal_dev.yml /app/deploy/
COPY quartz.properties /app/deploy/

CMD ["java", "-Dmail.smtp.ssl.protocols=TLSv1.2", "-Dhttps.protocols=TLSv1.2",  "-jar", "loreal-1.0-SNAPSHOT.jar", "server", "loreal_dev.yml"]