package com.jubiq.loreal.cra.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.cra.models.CRAStatus;
import com.jubiq.loreal.cra.models.CraRequest;
import com.jubiq.loreal.cra.services.CraRequestService;
import com.jubiq.loreal.cra.services.QuickLoginService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.util.Arrays;
import java.io.File;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by Hải hà on 7/9/25.
 * REST endpoint for CRA Request management
 */
@Path("/api/cra-requests")
@Api("CRA Requests")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED})
@Singleton
public class CraRequestEndpoint extends JubiqEndpoint<Long, CraRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CraRequestEndpoint.class);

    private CraRequestService craRequestService;

    @Inject
    private QuickLoginService quickLoginService;

    @Inject
    public CraRequestEndpoint(CraRequestService craRequestService) {
        this.service = this.craRequestService = craRequestService;
    }

    @POST
    @Path("/{id}/cancel")
    @ApiOperation("Cancel a CRA request")
    public Response doCancel(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("reason") String reason) throws JubiqPersistenceException, UnauthorizedException {
        craRequestService.cancel(id, reason, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/submit")
    @ApiOperation("Submit a CRA request")
    public Response doSubmit(@Auth JubiqSession session, @PathParam("id") Long id) throws JubiqPersistenceException, UnauthorizedException {
        craRequestService.submit(id, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/assign")
    @ApiOperation("Assign a CRA request")
    public Response doAssign(@Auth JubiqSession session, @PathParam("id") Long id, @FormParam("assigneeId") Long assigneeId) throws JubiqPersistenceException, UnauthorizedException {
        authorize(session, "API:CRA:ASSIGN");
        craRequestService.assign(id, assigneeId, session);
        return Response.ok().build();
    }

    @POST
    @Path("/{id}/save-risk-assessment")
    @ApiOperation("Save risk assessment for a CRA request")
    public Response doSaveRiskAssessment(@Auth JubiqSession session, @PathParam("id") Long id) throws JubiqPersistenceException, UnauthorizedException {
        authorize(session, "API:CRA:SAVE-RISK-ASSESSMENT");
        craRequestService.saveRiskAssessment(id, session);
        return Response.ok().build();
    }


    @GET
    @Path("/xls")
    @ApiOperation("Export CRA requests to Excel")
    public Response doExportToXls(@Auth JubiqSession session,
                                  @DefaultValue("") @QueryParam("query") String query,
                                  @DefaultValue("created desc") @QueryParam("order") String order,
                                  @QueryParam("fields") String fields) throws IOException, JubiqPersistenceException {

        List<CraRequest> craRequests = this.craRequestService.search(query, 10000, 0, order);
        List<String> fieldNames = Arrays.asList(fields.split(","));
        String sheetName = "CRA Requests";

        File file = this.craRequestService.exportXls(sheetName, craRequests, fieldNames, session.userId);
        Response.ResponseBuilder builder = Response.ok(file);
        builder.header("Content-Disposition", "attachment; filename=cra_requests.xls");
        return builder.build();
    }


}
