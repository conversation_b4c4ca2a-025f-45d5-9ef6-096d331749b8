--liquibase formatted sql
--changeset ai-assistant:2025-cra-email-templates

-- Email template for CRA request submission alert
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`)
VALUES ('submit-cra-request.alert.email.template',
        'Dear ${sourceFullName},<br><br>CRA Request ${craRequestId} (${exposition}) has been submitted and is waiting for STRD Director assignment.<br><br>Please click <a href="${webUrl}cra/process/${craRequestId}">here</a> to view and assign the request.<br><br>Best regards,<br>L''Oreal CRA System',
        'Email template for CRA request submission alert',
        NULL, NULL, NULL, NULL);

-- Email template for CRA request assignment alert
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`)
VALUES ('assign-cra-request.alert.email.template',
        'Dear <PERSON><PERSON><PERSON>,<br><br>CRA Request ${craRequestId} (${exposition}) has been assigned to you by ${sourceFullName}.<br><br>Please click <a href="${webUrl}cra/process/${craRequestId}">here</a> to view and start risk assessment.<br><br>Best regards,<br>L''Oreal CRA System',
        'Email template for CRA request assignment alert',
        NULL, NULL, NULL, NULL);

-- Email template for CRA request re-assignment alert
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`)
VALUES ('re-assign-cra-request.alert.email.template',
        'Dear Assignee,<br><br>CRA Request ${craRequestId} (${exposition}) has been re-assigned to you by ${sourceFullName}.<br><br>Please click <a href="${webUrl}cra/process/${craRequestId}">here</a> to view and continue risk assessment.<br><br>Best regards,<br>L''Oreal CRA System',
        'Email template for CRA request re-assignment alert',
        NULL, NULL, NULL, NULL);

-- Email template for CRA risk assessment completion alert
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`)
VALUES ('save-risk-assessment-cra-request.alert.email.template',
        'Dear ${sourceFullName},<br><br>Risk assessment for CRA Request ${craRequestId} (${exposition}) has been completed by ${sourceFullName}.<br><br>The request is now waiting for approval. You will receive approval links via email shortly.<br><br>Please click <a href="${webUrl}cra/process/${craRequestId}">here</a> to view the details.<br><br>Best regards,<br>L''Oreal CRA System',
        'Email template for CRA risk assessment completion alert',
        NULL, NULL, NULL, NULL);

-- Email template for CRA request cancellation alert
INSERT INTO `configurations` (`id`, `value`, `description`, `created`, `updated`, `deleted`, `creator_id`)
VALUES ('cancel-cra-request.alert.email.template',
        'Dear Team,<br><br>CRA Request ${craRequestId} (${exposition}) has been cancelled by ${sourceFullName}.<br><br>${reason}<br><br>Please click <a href="${webUrl}cra/process/${craRequestId}">here</a> to view the details.<br><br>Best regards,<br>L''Oreal CRA System',
        'Email template for CRA request cancellation alert',
        NULL, NULL, NULL, NULL);
