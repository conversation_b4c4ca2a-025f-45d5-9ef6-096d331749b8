package com.jubiq.loreal.cra.endpoints;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.common.endpoints.JubiqEndpoint;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.exceptions.UnauthorizedException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.cra.models.CraClaims;
import com.jubiq.loreal.cra.services.CraClaimsService;
import com.wordnik.swagger.annotations.Api;
import com.wordnik.swagger.annotations.ApiOperation;
import com.wordnik.swagger.annotations.ApiParam;
import io.dropwizard.auth.Auth;

import javax.ws.rs.*;
import javax.ws.rs.core.Response;
import java.util.List;

import static javax.ws.rs.core.MediaType.APPLICATION_FORM_URLENCODED;
import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

/**
 * Created by <PERSON><PERSON><PERSON> hà on 7/11/25.
 * REST endpoint for CRA Claims management
 */
@Path("/api/cra-claims")
@Api("CRA Claims")
@Produces(APPLICATION_JSON)
@Consumes({APPLICATION_JSON, APPLICATION_FORM_URLENCODED})
@Singleton
public class CraClaimsEndpoint extends JubiqEndpoint<Long, CraClaims> {

    private CraClaimsService craClaimsService;

    @Inject
    public CraClaimsEndpoint(CraClaimsService craClaimsService) {
        this.service = this.craClaimsService = craClaimsService;
    }
    
    @GET
    @Path("/by-request/{craId}")
    @ApiOperation("Get claims by CRA ID")
    public Response getClaimsByCraId(@Auth JubiqSession session,
                                    @PathParam("craId") Long craId) throws JubiqPersistenceException {

        List<CraClaims> claims = craClaimsService.getClaimsByRequestId(craId);
        return Response.ok(claims).build();
    }
    
    @PUT
    @Path("/{id}/approve")
    @ApiOperation("Approve or reject a claim")
    public Response approveClaim(@Auth JubiqSession session,
                                @PathParam("id") Long id,
                                @ApiParam @FormParam("approvalStatus") String approvalStatus,
                                @ApiParam @FormParam("approverType") String approverType) throws JubiqPersistenceException, UnauthorizedException {

        try {
            craClaimsService.approveClaim(id, approvalStatus, approverType, session);
            return Response.ok().build();
        } catch (EntityNotFoundException e) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
    }
    
    @PUT
    @Path("/{id}/mkt-accept")
    @ApiOperation("Marketing acceptance of a claim")
    public Response mktAcceptClaim(@Auth JubiqSession session,
                                  @PathParam("id") Long id,
                                  @ApiParam @FormParam("mktAcceptedStatus") String mktAcceptedStatus) throws JubiqPersistenceException, UnauthorizedException {

        try {
            craClaimsService.mktAcceptClaim(id, mktAcceptedStatus, session);
            return Response.ok().build();
        } catch (EntityNotFoundException e) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
    }

}
