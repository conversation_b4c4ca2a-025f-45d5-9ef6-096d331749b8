package com.jubiq.loreal.cra.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.jubiq.loreal.LorealConfiguration;
import com.jubiq.loreal.common.exceptions.EntityNotFoundException;
import com.jubiq.loreal.common.exceptions.JubiqPersistenceException;
import com.jubiq.loreal.common.model.JubiqSession;
import com.jubiq.loreal.common.persistence.DaoFactory;
import com.jubiq.loreal.common.service.HistoryService;
import com.jubiq.loreal.common.service.JubiqService;
import com.jubiq.loreal.common.service.MailService;
import com.jubiq.loreal.cra.models.CraClaims;
import com.jubiq.loreal.cra.models.CraRequest;
import com.jubiq.loreal.notifications.models.History;
import com.jubiq.loreal.umgr.models.User;
import com.jubiq.loreal.umgr.services.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 7/11/25.
 * Service for CRA Claims management
 */
@Singleton
public class CraClaimsService extends JubiqService<Long, CraClaims> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CraClaimsService.class);

    private HistoryService historyService;
    private CraRequestService craRequestService;
    private UserService userService;
    private MailService mailService;
    private LorealConfiguration configuration;

    @Inject
    public CraClaimsService(DaoFactory daoFactory, HistoryService historyService,
                           CraRequestService craRequestService, UserService userService,
                           MailService mailService, LorealConfiguration configuration) {
        this.dao = daoFactory.createDao(CraClaims.class, Long.class);
        this.historyService = historyService;
        this.craRequestService = craRequestService;
        this.userService = userService;
        this.mailService = mailService;
        this.configuration = configuration;
    }
    
    @Override
    public CraClaims create(CraClaims entity, JubiqSession session) throws JubiqPersistenceException {
        entity.creatorId = session != null ? session.userId : 1000L;
        beforeCreate(entity);
        CraClaims claims = this.dao.create(entity);
        
        if (session != null) {
            historyService.createHistory(new History(entity.id, "CRA_CLAIMS", "create", 
                session.userId, session.fullName, null, null, null));
        }
        
        return claims;
    }
    
    @Override
    public void update(Long id, CraClaims entity, JubiqSession session) throws JubiqPersistenceException, EntityNotFoundException {
        beforeUpdate(id, entity);
        this.dao.update(entity);

        if (session != null) {
            historyService.createHistory(new History(id, "CRA_CLAIMS", "update",
                session.userId, session.fullName, null, null, null));
        }

        afterUpdate(id, entity);
    }
    
    /**
     * Get claims by CRA request ID
     */
    public List<CraClaims> getClaimsByRequestId(Long craId) throws JubiqPersistenceException {
        String query = "cra_id = " + craId;
        return this.dao.search(query, 1000, 0, "created desc");
    }

    /**
     * Approve or reject a claim with email notifications
     */
    public void approveClaim(Long id, String approvalStatus, String approverType, JubiqSession session)
            throws JubiqPersistenceException {

        CraClaims claims = get(id);
        if (claims == null) {
            throw new EntityNotFoundException("CRA claim not found with id: " + id);
        }

        claims.approvalStatus = approvalStatus;
        claims.approvalDate = new Date();
        claims.approverId = session.userId;
        claims.approverType = approverType;

        update(id, claims, session);

        // Send approval emails based on approval status and approver type
        sendApprovalEmails(claims, approvalStatus, approverType);
    }

    /**
     * Marketing acceptance of a claim
     */
    public void mktAcceptClaim(Long id, String mktAcceptedStatus, JubiqSession session)
            throws JubiqPersistenceException {

        CraClaims claims = get(id);
        if (claims == null) {
            throw new EntityNotFoundException("CRA claim not found with id: " + id);
        }

        claims.mktAcceptedStatus = mktAcceptedStatus;
        claims.mktAcceptedDate = new Date();
        claims.mktAcceptedBy = session.userId;

        update(id, claims, session);
    }

    /**
     * Send approval emails based on approval status and approver type
     */
    private void sendApprovalEmails(CraClaims claims, String approvalStatus, String approverType) {
        try {
            // Get CRA request to access login hashes
            CraRequest craRequest = craRequestService.get(claims.craId);
            if (craRequest == null) {
                LOGGER.error("CRA request not found for claim {}", claims.id);
                return;
            }

            List<User> targetUsers = null;
            String emailSubject = null;
            String loginHash = null;

            // Determine target users and login hash based on approval status and approver type
            if ("PENDING_GM".equals(approvalStatus)) {
                // Check if approverType is one of the GM types
                List<String> gmTypes = Arrays.asList("GM", "GM-CPD", "GM-ACD", "GM-PPD", "GM-LUXE");
                if (gmTypes.contains(approverType)) {
                    targetUsers = userService.usersOfGroups(Arrays.asList(approverType));
                    emailSubject = "CRA Claim Approval Required - " + claims.id;
                    loginHash = craRequest.gmLoginHash;
                }
            } else if ("PENDING_CM".equals(approvalStatus) && "CM".equals(approverType)) {
                targetUsers = userService.usersOfGroups(Arrays.asList("CM"));
                emailSubject = "CRA Claim Approval Required - " + claims.id;
                loginHash = craRequest.cmLoginHash;
            }

            // Send emails to target users
            if (targetUsers != null && !targetUsers.isEmpty() && emailSubject != null && loginHash != null) {
                for (User user : targetUsers) {
                    if (user.email != null && !user.email.trim().isEmpty()) {
                        String emailBody = buildApprovalEmailBody(claims, approverType, loginHash, user.email);
                        mailService.sendMail(user.email, emailSubject, emailBody);
                        LOGGER.info("Approval email sent to {} ({}) for CRA claim {} with status {} and approver type {}",
                                   user.fullName, user.email, claims.id, approvalStatus, approverType);
                    }
                }
            } else {
                LOGGER.warn("No target users found or missing login hash for approval status {} and approver type {} for CRA claim {}",
                           approvalStatus, approverType, claims.id);
            }

        } catch (Exception e) {
            LOGGER.error("Failed to send approval emails for CRA claim {} with status {} and approver type {}",
                        claims.id, approvalStatus, approverType, e);
        }
    }

    /**
     * Build email body for approval notification with quick access link
     */
    private String buildApprovalEmailBody(CraClaims claims, String approverType, String loginHash, String userEmail) {
        // Build quick access link using webUrl from configuration
        String baseUrl = configuration.webUrl;
        String quickAccessLink = String.format("%s/loreal-webapp/approval/index?craRequestId=%d&loginHash=%s&email=%s",
                                               baseUrl, claims.craId, loginHash, userEmail);

        StringBuilder emailBody = new StringBuilder();
        emailBody.append("<html><body>");
        emailBody.append("<h3>CRA Claim Approval Required</h3>");
        emailBody.append("<p>Dear ").append(approverType).append(",</p>");
        emailBody.append("<p>A CRA claim requires your approval:</p>");
        emailBody.append("<ul>");
        emailBody.append("<li><strong>Claim ID:</strong> ").append(claims.id).append("</li>");
        emailBody.append("<li><strong>CRA ID:</strong> ").append(claims.craId).append("</li>");
        emailBody.append("<li><strong>Approval Status:</strong> ").append(claims.approvalStatus).append("</li>");
        emailBody.append("<li><strong>Approver Type:</strong> ").append(approverType).append("</li>");
        if (claims.detail != null) {
            emailBody.append("<li><strong>Detail:</strong> ").append(claims.detail).append("</li>");
        }
        emailBody.append("</ul>");
        emailBody.append("<p><strong>Quick Access:</strong></p>");
        emailBody.append("<p><a href=\"").append(quickAccessLink).append("\" style=\"background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">Click here to approve/reject this claim</a></p>");
        emailBody.append("<p>Or copy this link to your browser:<br>");
        emailBody.append("<a href=\"").append(quickAccessLink).append("\">").append(quickAccessLink).append("</a></p>");
        emailBody.append("<p>This link will automatically log you in as ").append(approverType).append(" for this specific approval.</p>");
        emailBody.append("<p>Best regards,<br>L'Oreal CRA System</p>");
        emailBody.append("</body></html>");
        return emailBody.toString();
    }

}
